import { cn } from "@/lib/utils";

type IconProps = React.HTMLAttributes<SVGElement>;

export const Icons = {
  aluminum: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M60.6554 42.7932H16V84.6961H60.6554V42.7932Z" />
      <path d="M81.2026 11.3913L75.8875 6.08307H77.8873C79.4919 6.08307 80.793 4.78368 80.793 3.1811V2.90197C80.793 1.29939 79.4919 0 77.8873 0H43.438C41.8334 0 40.5323 1.29939 40.5323 2.90197V3.1811C40.5323 4.78368 41.8334 6.08307 43.438 6.08307H45.4378L40.1227 11.3913C38.9758 12.5367 38.3349 14.0863 38.3349 15.7034V24.6643H21.1031C19.4984 24.6643 18.1974 25.9637 18.1974 27.5663V27.8454C18.1974 29.448 19.4984 30.7474 21.1031 30.7474H22.7655L17.7878 35.7188C16.8288 36.6765 16.2265 37.9181 16.0578 39.2464H60.5976C60.4241 37.9181 59.8266 36.6716 58.8677 35.7188L53.8851 30.7426H55.5475C57.1522 30.7426 58.4533 29.4432 58.4533 27.8406V27.5615C58.4533 25.9589 57.1522 24.6595 55.5475 24.6595H48.2471C51.2781 19.2839 55.2054 16.0354 59.5037 16.0354C69.0882 16.0354 76.8609 32.196 76.8609 52.1344C76.8609 72.0728 70.3074 88.2333 60.7229 88.2333H16.0578C16.2313 89.5568 16.8336 90.7936 17.7878 91.7513L24.2593 98.2145C25.4062 99.3599 26.9579 100 28.577 100H48.0833C49.7024 100 51.254 99.3599 52.4009 98.2145L55.0127 95.6061H70.423C72.0422 95.6061 73.5938 94.9661 74.7407 93.8207L81.2122 87.3574C82.3591 86.212 83 84.6624 83 83.0454V15.7034C83 14.0863 82.3591 12.5367 81.2122 11.3913H81.2026Z" />
    </svg>
  ),
  miscellaneous: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M65.4218 25.6965C62.4771 23.8895 60.8416 20.5636 61.324 17.1554L62.8768 6.16222C63.3362 2.90498 60.8003 0 57.4972 0H45.36C42.0569 0 39.521 2.90956 39.9804 6.16222L40.8992 12.6721C41.1381 14.3511 39.8288 15.8516 38.1244 15.8516H31.7067C21.5034 15.8516 13 23.9901 13 34.1553C13 39.4437 15.2602 44.2106 18.8711 47.5594C20.8052 49.3527 21.9353 51.8596 21.8756 54.4901C21.8664 54.7921 21.8664 55.094 21.8664 55.4005C21.8664 65.0899 25.4543 80.0448 31.1967 92.1543C31.2105 92.1863 31.2289 92.2229 31.2473 92.2549C33.5397 97.0218 38.446 99.9954 43.7521 99.9954H44.6709C46.9081 98.8883 48.4471 96.5918 48.4471 93.9339V80.0677C47.6661 80.2507 46.853 80.3559 46.0123 80.3559C40.2193 80.3559 35.5243 75.6805 35.5243 69.9117C35.5243 67.391 36.4201 65.0807 37.9131 63.2783C37.5869 62.3816 37.4078 61.4163 37.4078 60.4053C37.4078 56.4985 40.0769 53.2138 43.6969 52.2531C43.7291 47.5502 47.5697 43.744 52.3014 43.744C57.0332 43.744 60.8462 47.5273 60.9014 52.2119C64.6087 53.1131 67.3559 56.439 67.3559 60.4053C67.3559 61.4163 67.1767 62.3816 66.8506 63.2783C68.3436 65.0807 69.2394 67.391 69.2394 69.9117C69.2394 75.6805 64.5444 80.3559 58.7514 80.3559C57.0792 80.3559 55.4988 79.9625 54.0931 79.2717V93.9384C54.0931 96.5964 55.6321 98.8883 57.8693 100H59.1143C64.4203 100 69.3267 97.0264 71.6191 92.2595C71.6375 92.2275 71.6513 92.1909 71.6696 92.1588C77.4121 80.0448 81 65.0945 81 55.4051C81 42.5591 74.6971 31.3967 65.4264 25.7057L65.4218 25.6965ZM37.4859 25.6645C37.4859 25.6645 37.4537 25.6828 37.4353 25.6965C31.9318 29.0727 27.4756 34.384 24.7606 40.8253C24.0807 42.4356 21.8847 42.7147 20.8924 41.2782C19.4913 39.2378 18.6736 36.772 18.6827 34.1187C18.7057 27.1101 24.586 21.5152 31.624 21.5152H36.2409C38.4966 21.5152 39.397 24.475 37.4813 25.6645H37.4859Z" />
    </svg>
  ),
  beveragePackaging: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M77.0756 19.3752L64.0904 6.24318V2.2872C64.0904 1.0231 63.0787 0 61.8288 0H58.7848C57.5348 0 56.5232 1.0231 56.5232 2.2872V6.82066L51.5953 11.8043H32.1309C29.5905 11.8043 27.5357 13.8869 27.5357 16.4514V24.1361H32.4322V27.0053H48.6277V24.1361H51.5368C52.7148 24.1361 53.8074 24.7772 54.3874 25.8139L61.0644 37.6955H20V93.3658C20 97.0307 22.9361 100 26.5601 100H54.5043C58.1283 100 61.0644 97.0307 61.0644 93.3658V92.3927H72.4399C76.0639 92.3927 79 89.4234 79 85.7585V24.0678C79 22.3081 78.3076 20.6211 77.0801 19.3752H77.0756ZM40.5299 17.952C45.0037 17.952 48.6277 19.0069 48.6277 20.3119C48.6277 21.617 45.0037 22.6719 40.5299 22.6719C36.0562 22.6719 32.4322 21.617 32.4322 20.3119C32.4322 19.0069 36.0562 17.952 40.5299 17.952Z" />
    </svg>
  ),
  glass: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M75 42.0916C74.9156 36.9305 72.237 32.3964 68.1947 29.6996C66.3912 28.4981 65.3339 26.4679 65.3339 24.3192V1.70138C65.3339 0.762991 64.561 0 63.6104 0H55.5169C54.5662 0 53.7933 0.762991 53.7933 1.70138V24.3455C53.7933 26.4986 52.7094 28.5113 50.9059 29.7215C48.0985 31.6027 45.953 34.3697 44.8824 37.6146C44.5137 36.8647 44.1361 36.1193 43.7363 35.3826C42.5592 33.212 41.9417 30.7871 41.9417 28.3271V14.4617C41.9417 13.0498 40.7823 11.9053 39.352 11.9053H35.9271C34.4967 11.9053 33.3373 13.0498 33.3373 14.4617V28.3271C33.3373 30.7871 32.7199 33.212 31.5427 35.3826C26.5898 44.5166 24 54.7161 24 65.0778V91.3308V91.8264H24.0133C24.2265 97.4172 28.0912 99.4168 33.3818 100V62.3065C33.3818 59.5527 35.6428 57.3164 38.4369 57.3164H38.8633V47.2221C38.8633 46.0645 39.814 45.1261 40.9867 45.1261H44.687C45.8597 45.1261 46.8103 46.0645 46.8103 47.2221V57.3164H47.2367C50.0264 57.3164 52.2919 59.5483 52.2919 62.3065V91.0502C52.2919 97.2857 57.1782 99.9737 62.5887 99.9737H64.6854C68.2302 99.9737 71.5485 98.8204 73.4186 96.2684C73.4275 96.2552 73.4364 96.2464 73.4408 96.2377C73.4808 96.1807 73.5208 96.1193 73.5608 96.0579C73.8095 95.6852 74.0272 95.2949 74.2137 94.8871C74.7024 93.7996 74.9778 92.5236 74.9778 91.0502L74.9956 42.096L75 42.0916Z" />
    </svg>
  ),
  plastics: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M75.7685 14.832H67.3822V9.62358C67.3822 4.31434 63.3219 0 58.3313 0H42.3377C37.3472 0 33.2869 4.31892 33.2869 9.62358V14.832H24.2315C20.24 14.832 17 18.0689 17 22.0668V92.7651C17 96.7585 20.2354 100 24.2315 100H26.894C30.8856 100 34.1255 96.7631 34.1255 92.7651V65.2698C34.1255 58.7685 37.9017 53.1521 43.378 50.4883V49.0303C43.378 46.0823 45.4402 43.6202 48.199 43.0012V35.3629H42.576C41.2791 35.3629 40.2297 34.313 40.2297 33.0155C40.2297 31.7179 41.2791 30.668 42.576 30.668H54.7889C61.7409 30.668 67.3959 36.3257 67.3959 43.2809V43.932C67.3959 45.2295 66.3465 46.2794 65.0496 46.2794C63.7527 46.2794 62.7032 45.2295 62.7032 43.932V43.2809C62.7032 38.9161 59.1516 35.3629 54.7889 35.3629H52.8871V43.0012C55.6459 43.6202 57.7081 46.0823 57.7081 49.0303V50.4883C63.1844 53.1521 66.9606 58.7685 66.9606 65.2698V93.1686C66.9606 96.4697 64.6921 99.2389 61.6309 100H75.7685C79.76 100 83 96.7631 83 92.7651V22.0668C83 18.0735 79.7646 14.832 75.7685 14.832ZM38.0712 14.832V9.62358C38.0712 6.95521 39.9868 4.78199 42.3377 4.78199H58.3359C60.6914 4.78199 62.6024 6.95521 62.6024 9.62358V14.832H38.0712Z" />
    </svg>
  ),
  play: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 27 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0.857422 24.6407V4.3593C0.857422 1.1879 4.37171 -0.722218 7.033 1.00269L22.6786 11.1434C25.1112 12.72 25.1112 16.28 22.6786 17.8566L7.033 27.9973C4.37171 29.7222 0.857422 27.8121 0.857422 24.6407Z" />
    </svg>
  ),
  ferrousMetals: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M88.6272 37.5412C86.7119 36.9865 84.269 36.4065 81.1668 35.8455C74.1083 34.5658 65.4735 33.7526 56.3489 33.4942C59.4511 28.054 61.4984 20.2058 61.4984 17.5708C61.4984 13.3914 56.3489 10 50 10C43.6511 10 38.5016 13.3914 38.5016 17.5708C38.5016 20.2058 40.5489 28.054 43.6511 33.4942C34.5265 33.7526 25.8917 34.5658 18.8332 35.8455C15.731 36.4065 13.2881 36.9928 11.3728 37.5412C9.87189 37.9761 9.87189 40.1194 11.3728 40.5544C13.2881 41.1091 15.731 41.6891 18.8332 42.2501C27.5119 43.8261 38.577 44.6897 49.9937 44.6897C61.4104 44.6897 72.4818 43.8197 81.1542 42.2501C84.2565 41.6891 86.6993 41.1028 88.6147 40.5544C90.1155 40.1194 90.1155 37.9761 88.6147 37.5412H88.6272ZM43.0733 17.5582C43.0733 15.8373 46.1756 14.4442 50.0063 14.4442C53.837 14.4442 56.9392 15.8373 56.9392 17.5582C56.9392 19.2792 53.837 20.6723 50.0063 20.6723C46.1756 20.6723 43.0733 19.2792 43.0733 17.5582ZM50.0063 34.2317C47.9905 34.2317 46.3577 32.5927 46.3577 30.5692C46.3577 28.5457 47.9905 26.9067 50.0063 26.9067C52.0221 26.9067 53.6549 28.5457 53.6549 30.5692C53.6549 32.5927 52.0221 34.2317 50.0063 34.2317Z" />
      <path d="M50.0063 49.884C22.3876 49.884 0 45.0364 0 39.0478V80.0286C0.144436 85.9857 22.4755 90.8081 50 90.8081C77.5245 90.8081 99.8618 85.9857 100 80.0286V39.0478C100 45.0364 77.6124 49.884 49.9937 49.884H50.0063ZM76.3753 75.0928C76.3753 77.0154 74.9121 78.6229 72.9967 78.7805C65.7058 79.3793 57.9314 79.6882 49.9498 79.6882C41.9681 79.6882 34.269 79.3793 27.0095 78.7868C25.1005 78.6292 23.631 77.028 23.631 75.0991V64.8365C23.631 62.6743 25.471 60.9723 27.6124 61.1551C34.0744 61.7036 41.5034 62.044 49.9435 62.044C58.3836 62.044 65.9005 61.7036 72.3813 61.1488C74.529 60.966 76.3627 62.668 76.3627 64.8302V75.0928H76.3753Z" />
    </svg>
  ),
  cardboard: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M57.4374 32.5268H57.4817V57.9868L53.2698 55.543L49.0579 57.9868L44.8459 55.543L40.634 57.9868V32.5268H40.6783L41.9198 22.4184H4.8548L1.61827 28.7501C0.5542 30.8384 0 33.1267 0 35.4816V85.0463C0 88.8897 3.10353 92 6.9386 92H93.0614C96.8965 92 100 88.8897 100 85.0463V35.415C100 33.1267 99.468 30.8606 98.4482 28.8167L95.256 22.4184H56.2181L57.4595 32.5268H57.4374Z" />
      <path d="M91.2658 14.4427L89.2707 10.4438C88.517 8.9553 87.0095 8 85.3469 8H54.4669L55.2427 14.4427H91.2658Z" />
      <path d="M43.6932 8.02222H14.9191C13.2787 8.02222 11.7712 8.9553 11.0175 10.4216L8.95589 14.465H42.9173L43.6932 8.02222Z" />
    </svg>
  ),
  compositePackaging: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M25.0695 81.5126L26.2049 92.9582C26.2049 95.9197 36.8595 100 50.0055 100C63.1515 100 73.8061 95.9197 73.8061 92.9582L74.9415 81.5126C68.8894 83.1578 59.9656 84.1944 50.011 84.1944C40.0565 84.1944 31.1271 83.1524 25.075 81.5126H25.0695Z" />
      <path d="M17 21.8274V24.8821C17 25.9899 17.9866 27.0484 19.7615 28.0026L22.771 58.358C28.7184 60.4036 38.695 61.7418 50 61.7418C61.305 61.7418 71.2816 60.4036 77.229 58.358L80.2385 28.0026C82.0134 27.0484 83 25.9899 83 24.8821V21.8274C75.7573 25.8802 59.6294 27.0922 50 27.0922C40.3706 27.0922 24.2427 25.8802 17 21.8274Z" />
      <path d="M80.1007 13.4145L76.3691 2.61051C76.3691 6.04365 64.5625 8.82966 50 8.82966C35.4374 8.82966 23.6474 6.04914 23.6309 2.61599L19.9048 13.409C18.0362 14.3852 17 15.4656 17 16.6063V17.2206C17.3583 17.8677 19.2709 19.5185 25.7419 21.0486C32.2019 22.5732 40.8171 23.4123 50 23.4123C59.1829 23.4123 67.7981 22.5732 74.2581 21.0486C80.7291 19.524 82.6417 17.8677 83 17.2206V16.6063C83 15.4656 81.9638 14.3852 80.1007 13.409V13.4145Z" />
      <path d="M50 5.22102C42.9557 5.22102 37.3997 4.59033 33.4862 3.79511C32.2019 3.53735 32.2019 1.68915 33.4862 1.42591C37.3997 0.63069 42.9557 0 50 0C57.0443 0 62.6003 0.63069 66.5138 1.42591C67.7981 1.68367 67.7981 3.53186 66.5138 3.79511C62.6003 4.59033 57.0443 5.22102 50 5.22102Z" />
    </svg>
  ),
  calculator: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M76.8894 0H23.1106C15.3437 0 9 6.31324 9 14.0429V85.9571C9 93.6868 15.3437 100 23.1106 100H76.8894C84.6563 100 91 93.6868 91 85.9571V14.0429C91 6.31324 84.6563 0 76.8894 0ZM38.4818 78.5512C37.5465 79.482 36.2655 80.0081 34.9236 80.0081C33.5817 80.0081 32.3211 79.482 31.3655 78.5512C30.4302 77.6204 29.9016 76.3456 29.9016 75.0101C29.9016 73.6746 30.4302 72.4201 31.3655 71.469C32.3008 70.5382 33.5817 70.0121 34.9236 70.0121C36.2655 70.0121 37.5261 70.5382 38.4818 71.469C39.417 72.3998 39.9457 73.6746 39.9457 75.0101C39.9457 76.3456 39.417 77.6002 38.4818 78.5512ZM38.4818 58.5391C37.5465 59.4699 36.2655 59.996 34.9236 59.996C33.5817 59.996 32.3211 59.4699 31.3655 58.5391C30.4302 57.6083 29.9016 56.3335 29.9016 54.998C29.9016 53.6625 30.4302 52.408 31.3655 51.4569C32.3008 50.5261 33.5817 50 34.9236 50C36.2655 50 37.5261 50.5261 38.4818 51.4569C39.417 52.3877 39.9457 53.6625 39.9457 54.998C39.9457 56.3335 39.417 57.5881 38.4818 58.5391ZM53.548 78.5512C52.6127 79.482 51.3318 80.0081 49.9898 80.0081C48.6479 80.0081 47.3873 79.482 46.4317 78.5512C45.4964 77.6204 44.9678 76.3456 44.9678 75.0101C44.9678 73.6746 45.4964 72.4201 46.4317 71.469C47.367 70.5382 48.6479 70.0121 49.9898 70.0121C51.3318 70.0121 52.5924 70.5382 53.548 71.469C54.4833 72.3998 55.0119 73.6746 55.0119 75.0101C55.0119 76.3456 54.4833 77.6002 53.548 78.5512ZM53.548 58.5391C52.6127 59.4699 51.3318 59.996 49.9898 59.996C48.6479 59.996 47.3873 59.4699 46.4317 58.5391C45.4964 57.6083 44.9678 56.3335 44.9678 54.998C44.9678 53.6625 45.4964 52.408 46.4317 51.4569C47.367 50.5261 48.6479 50 49.9898 50C51.3318 50 52.5924 50.5261 53.548 51.4569C54.4833 52.3877 55.0119 53.6625 55.0119 54.998C55.0119 56.3335 54.4833 57.5881 53.548 58.5391ZM68.6142 78.5512C67.6789 79.482 66.398 80.0081 65.056 80.0081C63.7141 80.0081 62.4535 79.482 61.4979 78.5512C60.5626 77.6204 60.034 76.3456 60.034 75.0101C60.034 73.6746 60.5626 72.4201 61.4979 71.469C62.4332 70.5382 63.7141 70.0121 65.056 70.0121C66.398 70.0121 67.6586 70.5382 68.6142 71.469C69.5495 72.3998 70.0781 73.6746 70.0781 75.0101C70.0781 76.3456 69.5495 77.6002 68.6142 78.5512ZM68.6142 58.5391C67.6789 59.4699 66.398 59.996 65.056 59.996C63.7141 59.996 62.4535 59.4699 61.4979 58.5391C60.5626 57.6083 60.034 56.3335 60.034 54.998C60.034 53.6625 60.5626 52.408 61.4979 51.4569C62.4332 50.5261 63.7141 50 65.056 50C66.398 50 67.6586 50.5261 68.6142 51.4569C69.5495 52.3877 70.0781 53.6625 70.0781 54.998C70.0781 56.3335 69.5495 57.5881 68.6142 58.5391ZM69.4072 27.499C68.5125 29.0368 66.8453 30.0081 65.056 30.0081H34.9033C33.1141 30.0081 31.4468 29.057 30.5522 27.499C29.6576 25.9611 29.6576 24.0388 30.5522 22.501C31.4468 20.9632 33.1141 19.9919 34.9033 19.9919H65.056C66.8453 19.9919 68.5125 20.9429 69.4072 22.501C70.3018 24.0591 70.3018 25.9611 69.4072 27.499Z" />
    </svg>
  ),
  questionMarkCalculator: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M23.1106 0H76.8894C84.6563 0 91 6.31324 91 14.0429V85.9571C91 93.6868 84.6563 100 76.8894 100H23.1106C15.3437 100 9 93.6868 9 85.9571V14.0429C9 6.31324 15.3437 0 23.1106 0ZM34.9236 80.0081C36.2655 80.0081 37.5465 79.482 38.4818 78.5512C39.417 77.6002 39.9457 76.3456 39.9457 75.0101C39.9457 73.6746 39.417 72.3998 38.4818 71.469C37.5261 70.5382 36.2655 70.0121 34.9236 70.0121C33.5817 70.0121 32.3008 70.5382 31.3655 71.469C30.4302 72.4201 29.9016 73.6746 29.9016 75.0101C29.9016 76.3456 30.4302 77.6204 31.3655 78.5512C32.3211 79.482 33.5817 80.0081 34.9236 80.0081ZM34.9236 59.996C36.2655 59.996 37.5465 59.4699 38.4818 58.5391C39.417 57.5881 39.9457 56.3335 39.9457 54.998C39.9457 53.6625 39.417 52.3877 38.4818 51.4569C37.5261 50.5261 36.2655 50 34.9236 50C33.5817 50 32.3008 50.5261 31.3655 51.4569C30.4302 52.408 29.9016 53.6625 29.9016 54.998C29.9016 56.3335 30.4302 57.6083 31.3655 58.5391C32.3211 59.4699 33.5817 59.996 34.9236 59.996ZM49.9898 80.0081C51.3318 80.0081 52.6127 79.482 53.548 78.5512C54.4833 77.6002 55.0119 76.3456 55.0119 75.0101C55.0119 73.6746 54.4833 72.3998 53.548 71.469C52.5924 70.5382 51.3318 70.0121 49.9898 70.0121C48.6479 70.0121 47.367 70.5382 46.4317 71.469C45.4964 72.4201 44.9678 73.6746 44.9678 75.0101C44.9678 76.3456 45.4964 77.6204 46.4317 78.5512C47.3873 79.482 48.6479 80.0081 49.9898 80.0081ZM49.9898 59.996C51.3318 59.996 52.6127 59.4699 53.548 58.5391C54.4833 57.5881 55.0119 56.3335 55.0119 54.998C55.0119 53.6625 54.4833 52.3877 53.548 51.4569C52.5924 50.5261 51.3318 50 49.9898 50C48.6479 50 47.367 50.5261 46.4317 51.4569C45.4964 52.408 44.9678 53.6625 44.9678 54.998C44.9678 56.3335 45.4964 57.6083 46.4317 58.5391C47.3873 59.4699 48.6479 59.996 49.9898 59.996ZM65.056 80.0081C66.398 80.0081 67.6789 79.482 68.6142 78.5512C69.5495 77.6002 70.0781 76.3456 70.0781 75.0101C70.0781 73.6746 69.5495 72.3998 68.6142 71.469C67.6586 70.5382 66.398 70.0121 65.056 70.0121C63.7141 70.0121 62.4332 70.5382 61.4979 71.469C60.5626 72.4201 60.034 73.6746 60.034 75.0101C60.034 76.3456 60.5626 77.6204 61.4979 78.5512C62.4535 79.482 63.7141 80.0081 65.056 80.0081ZM65.056 59.996C66.398 59.996 67.6789 59.4699 68.6142 58.5391C69.5495 57.5881 70.0781 56.3335 70.0781 54.998C70.0781 53.6625 69.5495 52.3877 68.6142 51.4569C67.6586 50.5261 66.398 50 65.056 50C63.7141 50 62.4332 50.5261 61.4979 51.4569C60.5626 52.408 60.034 53.6625 60.034 54.998C60.034 56.3335 60.5626 57.6083 61.4979 58.5391C62.4535 59.4699 63.7141 59.996 65.056 59.996ZM50.0593 16C58.1271 16 64 21.2941 64 28.5963C64 32.4909 62.4576 35.5335 59.3136 38.3935C58.808 38.8456 58.2629 39.286 57.7195 39.725C55.7761 41.2951 53.8559 42.8465 53.8559 44.8438V46H45.2542V43.931C45.2542 41.071 47.2119 38.0284 51.0085 35.3509C53.7966 33.4037 55.161 31.3955 55.161 29.3266C55.161 26.7099 53.0847 24.5193 50 24.5193C46.9153 24.5193 44.661 26.9533 44.6017 30.3002H36C36 21.9026 41.6949 16 50.0593 16Z"
      />
    </svg>
  ),
  lightBulb: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M57.093 91.2383C56.0994 91.2383 55.2965 90.4361 55.2965 89.4434C55.2965 88.4507 56.0994 87.6484 57.093 87.6484H59.2977V84.0631H57.093C56.0994 84.0631 55.2965 83.2608 55.2965 82.2682C55.2965 81.2755 56.0994 80.4732 57.093 80.4732H59.4656C59.6017 79.272 59.8149 78.08 60.1143 76.9151H40.8928C41.2104 78.08 41.4463 79.2675 41.6005 80.4732H43.9369C44.9304 80.4732 45.7333 81.2755 45.7333 82.2682C45.7333 83.2608 44.9304 84.0631 43.9369 84.0631H41.8001V87.6484H43.9369C44.9304 87.6484 45.7333 88.4507 45.7333 89.4434C45.7333 90.4361 44.9304 91.2383 43.9369 91.2383H41.8273C42.0542 93.8174 43.8733 95.9297 46.2959 96.6096C46.2959 96.6458 46.2868 96.6775 46.2868 96.7138C46.2868 98.5269 48.1967 100 50.5512 100C52.9057 100 54.8156 98.5269 54.8156 96.7138C54.8156 96.6775 54.8111 96.6458 54.8065 96.6096C57.229 95.9297 59.0482 93.8174 59.2751 91.2383H57.093Z" />
      <path d="M50.1021 25.7864C37.6673 25.9587 27.5144 36.0937 27.3239 48.5178C27.2286 54.9995 29.8054 60.883 34.0289 65.1346C36.4061 67.5279 38.2979 70.3109 39.6226 73.3388H61.3165C62.5777 70.3291 64.4059 67.5641 66.7559 65.2162C70.9477 61.0371 73.5426 55.2579 73.5426 48.8714C73.5426 36.0076 63.0177 25.6051 50.1021 25.7819V25.7864Z" />
      <path d="M49.9977 19.1687C48.3918 19.1687 47.0898 17.8678 47.0898 16.2633V2.90545C47.0898 1.30088 48.3918 0 49.9977 0C51.6037 0 52.9057 1.30088 52.9057 2.90545V16.2678C52.9057 17.8724 51.6037 19.1732 49.9977 19.1732V19.1687Z" />
      <path d="M74.9217 26.217C74.1777 26.217 73.4337 25.9315 72.8667 25.3649C71.7325 24.2317 71.7325 22.3914 72.8667 21.2583L82.3209 11.8122C83.4551 10.679 85.2969 10.679 86.4311 11.8122C87.5652 12.9453 87.5652 14.7856 86.4311 15.9188L76.9768 25.3649C76.4097 25.9315 75.6657 26.217 74.9217 26.217Z" />
      <path d="M97.092 51.4097H83.7182C82.1122 51.4097 80.8102 50.1088 80.8102 48.5042C80.8102 46.8997 82.1122 45.5988 83.7182 45.5988H97.092C98.698 45.5988 100 46.8997 100 48.5042C100 50.1088 98.698 51.4097 97.092 51.4097Z" />
      <path d="M25.0737 26.217C24.3297 26.217 23.5857 25.9315 23.0186 25.3649L13.5644 15.9188C12.4303 14.7856 12.4303 12.9453 13.5644 11.8122C14.6985 10.679 16.5404 10.679 17.6745 11.8122L27.1288 21.2583C28.2629 22.3914 28.2629 24.2317 27.1288 25.3649C26.5617 25.9315 25.8177 26.217 25.0737 26.217Z" />
      <path d="M2.90795 51.4097C1.302 51.4097 0 50.1088 0 48.5042C0 46.8997 1.302 45.5988 2.90795 45.5988H16.2818C17.8878 45.5988 19.1898 46.8997 19.1898 48.5042C19.1898 50.1088 17.8878 51.4097 16.2818 51.4097H2.90795Z" />
    </svg>
  ),
  pencil: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M88.5271 45.2015C84.7612 45.2015 81.7158 48.2652 81.7158 52.0537C81.7158 70.9384 66.4409 86.3052 47.6691 86.3052C28.8974 86.3052 13.6225 70.9384 13.6225 52.0537C13.6225 33.169 28.8974 17.8023 47.6691 17.8023C48.8316 17.8023 50.0036 17.8602 51.1564 17.9762C54.8935 18.3628 58.2367 15.6181 58.621 11.8488C59.0052 8.0893 56.2769 4.72601 52.5302 4.33942C50.9259 4.17512 49.2831 4.08814 47.6691 4.08814C21.3848 4.08814 0 25.6016 0 52.0441C0 78.4865 21.3848 100 47.6691 100C73.9535 100 95.3383 78.4865 95.3383 52.0441C95.3383 48.2555 92.2929 45.1918 88.5271 45.1918V45.2015Z" />
      <path d="M97.6439 3.27631L96.6929 2.31951C93.6187 -0.773171 88.6423 -0.773171 85.5681 2.31951L47.7748 40.3402C46.3145 41.8092 46.3145 44.1771 47.7748 45.6461L54.7013 52.6143C56.1616 54.0833 58.5152 54.0833 59.9755 52.6143L97.6439 14.7192C100.785 11.5589 100.785 6.42698 97.6439 3.25698V3.27631Z" />
      <path d="M40.6369 49.6376L34.0947 64.2505C33.4414 65.7195 34.9401 67.2175 36.4003 66.541L50.8106 59.8338C52.6263 58.9833 53.0586 56.5768 51.6368 55.1561L45.3059 48.7871C43.8841 47.3567 41.4727 47.8013 40.6465 49.6472L40.6369 49.6376Z" />
    </svg>
  ),
  login: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M70.2629 49.4088C67.872 46.8775 64.0943 46.0876 60.9096 47.5151C57.4188 49.0757 53.555 49.9417 49.4904 49.9417C45.4258 49.9417 41.5525 49.0757 38.0713 47.5151C34.8866 46.0876 31.1089 46.8775 28.718 49.4088C23.9361 54.481 21 61.3042 21 68.8031V90.4718C21 94.3259 23.8117 97.6185 27.6372 98.2371C42.1168 100.588 56.8832 100.588 71.3627 98.2371C75.1883 97.6185 78 94.3259 78 90.4718V68.8031C78 61.2947 75.0639 54.481 70.282 49.4088H70.2629Z" />
      <path d="M49.4904 44.2319C61.7656 44.2319 71.7166 34.3303 71.7166 22.116C71.7166 9.90165 61.7656 0 49.4904 0C37.2153 0 27.2642 9.90165 27.2642 22.116C27.2642 34.3303 37.2153 44.2319 49.4904 44.2319Z" />
    </svg>
  ),
  regulator: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M53.9474 76.9737V96.0526C53.9474 98.2895 52.2368 100 50 100C47.7632 100 46.0526 98.2895 46.0526 96.0526V76.9737C40.6579 75.2632 36.8421 70.2632 36.8421 64.4737C36.8421 58.6842 40.6579 53.5526 46.0526 51.9737V3.94737C46.0526 1.71053 47.7632 0 50 0C52.2368 0 53.9474 1.71053 53.9474 3.94737V51.9737C59.3421 53.6842 63.1579 58.6842 63.1579 64.4737C63.1579 70.2632 59.3421 75.3947 53.9474 76.9737ZM50 59.2082C47.1053 59.2082 44.7368 61.5766 44.7368 64.4713C44.7368 67.3661 47.1053 69.7345 50 69.7345C52.8947 69.7345 55.2632 67.3661 55.2632 64.4713C55.2632 61.5766 52.8947 59.2082 50 59.2082Z" />
      <path d="M82.8947 26.9737V3.94737C82.8947 1.71053 84.6053 0 86.8421 0C89.0789 0 90.7895 1.71053 90.7895 3.94737V26.9737C96.1842 28.6842 100 33.6842 100 39.4737C100 45.2632 96.1842 50.3947 90.7895 51.9737V96.0526C90.7895 98.2895 89.0789 100 86.8421 100C84.6053 100 82.8947 98.2895 82.8947 96.0526V51.9737C77.5 50.2632 73.6842 45.2632 73.6842 39.4737C73.6842 33.6842 77.5 28.5526 82.8947 26.9737ZM86.8421 44.7368C89.7368 44.7368 92.1053 42.3684 92.1053 39.4737C92.1053 36.5789 89.7368 34.2105 86.8421 34.2105C83.9474 34.2105 81.5789 36.5789 81.5789 39.4737C81.5789 42.3684 83.9474 44.7368 86.8421 44.7368Z" />
      <path d="M17.1053 3.94737V26.9737C22.5 28.6842 26.3158 33.6842 26.3158 39.4737C26.3158 45.2632 22.5 50.3947 17.1053 51.9737V96.0526C17.1053 98.2895 15.3947 100 13.1579 100C10.9211 100 9.21053 98.2895 9.21053 96.0526V51.9737C3.81579 50.2632 0 45.2632 0 39.4737C0 33.6842 3.81579 28.5526 9.21053 26.9737V3.94737C9.21053 1.71053 10.9211 0 13.1579 0C15.3947 0 17.1053 1.71053 17.1053 3.94737ZM13.1579 44.7368C16.0526 44.7368 18.4211 42.3684 18.4211 39.4737C18.4211 36.5789 16.0526 34.2105 13.1579 34.2105C10.2632 34.2105 7.89474 36.5789 7.89474 39.4737C7.89474 42.3684 10.2632 44.7368 13.1579 44.7368Z" />
    </svg>
  ),
  badge: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M86.4425 39.3459C85.7007 37.6767 85.7007 35.7759 86.4425 34.1067C87.517 31.6908 87.0113 28.8804 85.1585 26.9699C83.8779 25.6495 83.2159 23.8661 83.329 22.0469C83.4953 19.4158 82.0384 16.9445 79.6334 15.7708C77.9668 14.959 76.7228 13.5049 76.1972 11.7574C75.4354 9.23068 73.2067 7.39514 70.5356 7.09846C68.6895 6.89306 67.0096 5.94431 65.9053 4.48045C64.3086 2.36126 61.5709 1.38318 58.9597 2.00263C57.1535 2.42973 55.2441 2.10044 53.694 1.09301C51.4553 -0.364337 48.5414 -0.364337 46.3027 1.09301C44.7526 2.10044 42.8465 2.42973 41.0403 2.00263C38.4257 1.38644 35.6914 2.36126 34.0948 4.48045C32.9904 5.94757 31.3139 6.89632 29.4644 7.09846C26.7933 7.39514 24.5613 9.22742 23.8028 11.7574C23.2773 13.5049 22.0332 14.959 20.3666 15.7708C17.9583 16.9445 16.5047 19.4158 16.671 22.0469C16.7874 23.8661 16.1255 25.6495 14.8415 26.9699C12.9887 28.8804 12.483 31.694 13.5575 34.1067C14.2993 35.7759 14.2993 37.6767 13.5575 39.3459C12.483 41.7618 12.9887 44.5722 14.8415 46.4827C16.1221 47.8031 16.7841 49.5865 16.671 51.4057C16.5047 54.0368 17.9616 56.5081 20.3666 57.6818C22.0332 58.4936 23.2773 59.9477 23.8028 61.6952C24.5646 64.2219 26.7933 66.0574 29.4644 66.3541C31.3105 66.5595 32.9904 67.5083 34.0948 68.9754C35.6914 71.0946 38.4291 72.0727 41.0403 71.4532C42.8465 71.0261 44.7559 71.3554 46.3027 72.3628C48.5414 73.8202 51.4553 73.8202 53.694 72.3628C55.2441 71.3554 57.1501 71.0261 58.9597 71.4532C61.5743 72.0694 64.3086 71.0946 65.9053 68.9754C67.0096 67.5083 68.6861 66.5595 70.5356 66.3541C73.2067 66.0574 75.4388 64.2252 76.1972 61.6952C76.7228 59.9477 77.9668 58.4936 79.6334 57.6818C82.0417 56.5081 83.4953 54.0368 83.329 51.4057C83.2126 49.5865 83.8745 47.8031 85.1585 46.4827C87.0113 44.5722 87.517 41.7618 86.4425 39.3459ZM70.9248 32.62L62.2595 40.8978L64.3052 52.5892C64.6512 54.5715 62.5289 56.0842 60.7127 55.1485L50.0017 49.6289L39.2906 55.1485C37.4744 56.0842 35.3521 54.5715 35.6981 52.5892L37.7438 40.8978L29.0785 32.62C27.6082 31.2148 28.4199 28.7696 30.4523 28.4794L42.4274 26.7743L47.7829 16.1392C48.6911 14.3363 51.3156 14.3363 52.2237 16.1392L57.5792 26.7743L69.5543 28.4794C71.5868 28.7696 72.3951 31.2148 70.9281 32.62H70.9248Z"
      />
      <path d="M41.8193 74.6212C37.8975 75.5471 33.816 74.0931 31.4176 70.911C30.8654 70.1775 30.0172 69.6982 29.0925 69.5939C27.3095 69.3982 25.6663 68.7462 24.2858 67.7518L13.6346 89.8826C13.0791 91.0368 14.0238 92.3441 15.3211 92.2105L24.1261 91.3074C24.7448 91.2454 25.3502 91.5225 25.6929 92.0311L30.5827 99.2657C31.3012 100.332 32.9345 100.214 33.49 99.0603L44.9029 75.3418L44.8484 75.3102C44.7196 75.2358 44.5891 75.1605 44.4638 75.0777C43.6888 74.5723 42.7241 74.406 41.8193 74.6212Z" />
      <path d="M58.1823 74.6197C62.1041 75.5456 66.1856 74.0916 68.584 70.9095L68.5873 70.9063C69.1395 70.1727 69.9877 69.6902 70.9125 69.5891C72.6921 69.3935 74.3387 68.7414 75.7191 67.747L86.367 89.8811C86.9225 91.0353 85.9778 92.3426 84.6805 92.2089L75.8755 91.3059C75.2568 91.2406 74.6513 91.521 74.3087 92.0296L69.4189 99.2642C68.6971 100.33 67.0671 100.213 66.5116 99.0588L55.0987 75.3402C55.1475 75.3109 55.1966 75.2823 55.2458 75.2537C55.3441 75.1964 55.4424 75.1392 55.5378 75.0762C56.3128 74.5708 57.2775 74.4045 58.1823 74.6197Z" />
    </svg>
  ),
  globe: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M68.7566 12.3921C67.1035 8.85851 65.2365 5.87602 63.1993 3.47842V3.48325C62.6207 2.80167 63.2819 1.7914 64.1425 2.03792C69.4567 3.57993 74.4112 5.96786 78.8405 9.05187C79.4483 9.47725 79.317 10.415 78.6072 10.6422C75.9038 11.522 72.9477 12.2857 69.7631 12.9093C69.3498 12.9866 68.9316 12.7691 68.7566 12.3921Z" />
      <path d="M36.921 3.48481C34.8887 5.86791 33.0265 8.84074 31.3783 12.355V12.3598C31.2032 12.7368 30.7851 12.9544 30.3718 12.8722C27.2066 12.2438 24.2651 11.4752 21.5812 10.5954C20.8762 10.3634 20.7401 9.43047 21.3527 9.00509C25.7626 5.95009 30.6878 3.57666 35.9729 2.04432C36.8335 1.79296 37.4996 2.80323 36.921 3.48481Z" />
      <path d="M38.9685 13.1861C42.2837 7.07737 46.2599 3.602 50.0564 3.60052C53.8578 3.602 57.8437 7.08704 61.1589 13.2103C61.3923 13.6405 61.1152 14.177 60.6241 14.2254C57.3033 14.5444 53.8902 14.7184 50.1707 14.7184C46.4512 14.7184 42.8873 14.5347 39.4984 14.2012C39.0074 14.1529 38.7351 13.6163 38.9685 13.1861Z" />
      <path d="M50.0564 3.60052L50.0588 3.60052H50.054L50.0564 3.60052Z" />
      <path d="M65.3477 23.8339C64.8615 22.171 63.2522 21.0979 61.5213 21.2719C57.8164 21.6393 53.9997 21.8327 50.1198 21.8327C46.2398 21.8327 42.2724 21.6345 38.4994 21.2526C36.7637 21.0786 35.1543 22.1469 34.6681 23.8146C32.9032 29.8617 31.6828 37.0449 31.3279 45.0594C31.2938 45.8183 31.9113 46.4467 32.6698 46.4467H67.346C68.1094 46.4467 68.722 45.8135 68.688 45.0594C68.3282 37.0545 67.1127 29.8811 65.3526 23.8387L65.3477 23.8339Z" />
      <path d="M39.3849 85.6888C42.7932 85.3504 46.3765 85.1667 50.1203 85.1667L50.1155 85.1716C53.7815 85.1716 57.2919 85.3456 60.6321 85.6695C61.1232 85.7178 61.4003 86.2495 61.167 86.6846C57.8364 92.8768 53.8204 96.4007 50.0036 96.4007C46.1869 96.4007 42.1806 92.8816 38.8549 86.7039C38.6216 86.2689 38.8938 85.7371 39.3849 85.6888Z" />
      <path d="M36.8592 96.5216C34.8123 94.1143 32.9307 91.1077 31.2727 87.5451C31.0928 87.1632 30.6795 86.9457 30.2614 87.0279C27.0719 87.6611 24.1109 88.4393 21.4076 89.3288C20.7026 89.5608 20.5713 90.4937 21.1791 90.9191C25.6181 94.0176 30.5823 96.4152 35.916 97.9621C36.7766 98.2086 37.4378 97.1983 36.8592 96.5216Z" />
      <path d="M32.7573 53.558H67.2536L67.2487 53.5629C68.0607 53.5629 68.7171 54.2348 68.6782 55.042C68.3184 62.9599 67.1175 70.0609 65.3817 76.0549C64.9004 77.7225 63.2862 78.7957 61.5504 78.6217C57.8358 78.2495 54.0871 78.0561 50.1197 78.0561C46.1523 78.0561 42.2529 78.2543 38.4702 78.641C36.7344 78.815 35.1202 77.7419 34.634 76.0742C32.8885 70.0754 31.6876 62.9647 31.3278 55.0372C31.2938 54.2299 31.9453 53.558 32.7573 53.558Z" />
      <path d="M68.7515 87.5023C67.0838 91.0793 65.2022 94.1005 63.1455 96.5174C62.567 97.1942 63.2331 98.2093 64.0888 97.9579C69.4516 96.4014 74.4401 93.9845 78.8986 90.8618C79.5064 90.4364 79.3702 89.5035 78.6653 89.2715C75.9474 88.3868 72.9669 87.6086 69.7579 86.9802C69.3447 86.898 68.9265 87.1156 68.7515 87.4974V87.5023Z" />
      <path d="M75.8435 45.1615C75.5226 37.495 74.4481 30.1668 72.6977 23.5976L72.7026 23.6024C72.1532 21.5335 73.3882 19.4308 75.474 18.9136C78.4253 18.1837 81.2355 17.3184 83.861 16.3323C85.3586 15.7667 87.0506 16.2211 88.0959 17.4296C94.6354 24.9801 98.9383 34.498 99.9934 44.9681C100.071 45.7561 99.4537 46.4473 98.6563 46.4473H77.1806C76.461 46.4473 75.8727 45.8769 75.8435 45.1615Z" />
      <path d="M75.8419 54.8436C75.5259 62.4714 74.4611 69.7609 72.7253 76.3011C72.1759 78.3652 73.4109 80.4679 75.4967 80.9851C78.4674 81.7199 81.2874 82.59 83.9275 83.5858C85.4299 84.1513 87.1267 83.6969 88.1721 82.4836C94.6678 74.9476 98.9416 65.4636 99.9918 55.0369C100.07 54.2442 99.4521 53.5578 98.6547 53.5578H77.179C76.4594 53.5578 75.8711 54.1282 75.8419 54.8436Z" />
      <path d="M24.159 54.8436C24.4799 62.4908 25.5496 69.7996 27.2902 76.3543L27.2853 76.3495C27.8299 78.4087 26.5998 80.5114 24.5237 81.0335C21.5724 81.7731 18.7621 82.6432 16.1366 83.6438C14.6342 84.2142 12.9374 83.7646 11.8872 82.5513C5.36229 75.0056 1.06422 65.4974 0.00915345 55.0369C-0.0686396 54.249 0.548843 53.5578 1.34622 53.5578H22.822C23.5415 53.5578 24.1299 54.1282 24.159 54.8436Z" />
      <path d="M24.1614 45.1561C24.4823 37.4703 25.5616 30.1228 27.3168 23.5439C27.8663 21.4847 26.6362 19.3771 24.5552 18.8599C21.6185 18.1251 18.8228 17.2598 16.2119 16.2689C14.7095 15.6985 13.0175 16.1481 11.9673 17.3565C5.39379 24.9167 1.06655 34.4636 0.00662125 44.9628C-0.0711718 45.7555 0.54631 46.4419 1.34369 46.4419H22.8194C23.539 46.4419 24.1273 45.8715 24.1565 45.1561H24.1614Z" />
    </svg>
  ),
  lock: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M39.3558 39.9626H78.8777L78.8729 39.9675C81.1539 39.9675 83 41.8288 83 44.1285V95.839C83 98.1387 81.1539 100 78.8729 100H22.1271C19.8461 100 18 98.1387 18 95.839V44.1285C18 41.8288 19.8461 39.9675 22.1271 39.9675H25.3699C26.5636 39.9675 27.5301 38.993 27.5301 37.7896V23.1627C27.5301 10.2412 38.0751 -0.244163 50.9446 0.00432743C63.5242 0.243074 73.4747 10.8648 73.4747 23.5476V29.2386C73.4747 30.6126 72.3729 31.7235 71.01 31.7235H66.274C64.9112 31.7235 63.8093 30.6126 63.8093 29.2386V23.1578C63.8093 15.6252 57.3383 9.52981 50.0989 9.74907C42.8595 9.96833 37.1955 16.1465 37.1955 23.4453V37.7847C37.1955 38.9882 38.1621 39.9626 39.3558 39.9626ZM36.3079 54.0062L48.67 50.3226C49.5157 50.0693 50.4146 50.0693 51.2554 50.3226L63.6175 54.0062C65.5506 54.586 66.8796 56.3741 66.8796 58.4108V67.7121C66.8796 76.4532 60.7179 86.5585 52.0239 91.0362C50.7287 91.7037 49.1919 91.7037 47.9016 91.0362C39.2027 86.5585 33.0458 76.4532 33.0458 67.7121V58.4108C33.0458 56.3741 34.3748 54.5811 36.3079 54.0062Z"
      />
    </svg>
  ),
  speechBubble: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.91586 7H91.0841C96.0095 7 100 10.9638 100 15.8459V62.2993C100 67.186 96.0095 71.1452 91.0841 71.1452H45.1979C43.0957 71.1452 41.3934 72.8341 41.3934 74.9198V89.8429C41.3934 92.6485 37.975 94.0559 35.9751 92.0716L16.8039 73.051C15.576 71.8328 13.9063 71.1452 12.1669 71.1452H8.91586C3.99051 71.1452 0 67.186 0 62.2993V15.8459C0 10.9592 3.99051 7 8.91586 7ZM56.3533 33.4533H67.39C69.2132 33.4533 70.6922 34.9207 70.6922 36.7295V41.404C70.6922 43.2128 69.2132 44.6802 67.39 44.6802H56.3533C55.7115 44.6802 55.1906 45.197 55.1906 45.8338V56.7839C55.1906 58.5927 53.7116 60.0601 51.8884 60.0601H47.177C45.3538 60.0601 43.8748 58.5927 43.8748 56.7839V45.8338C43.8748 45.197 43.3539 44.6802 42.7121 44.6802H31.6754C29.8522 44.6802 28.3732 43.2128 28.3732 41.404V36.7295C28.3732 34.9207 29.8522 33.4533 31.6754 33.4533H42.7121C43.3539 33.4533 43.8748 32.9365 43.8748 32.2997V21.3496C43.8748 19.5408 45.3538 18.0734 47.177 18.0734H51.8884C53.7116 18.0734 55.1906 19.5408 55.1906 21.3496V32.2997C55.1906 32.9365 55.7115 33.4533 56.3533 33.4533Z"
        fill="#002652"
      />
    </svg>
  ),

  leaf: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M62.3415 52.5503C96.3093 48.4962 96.7283 22.5759 99.9701 5.07973C100.176 3.95984 99.2885 2.93965 98.1514 3.00278C76.4493 4.1991 51.6816 8.42942 49.128 46.2099C39.3792 29.8037 23.1634 25.3374 1.43805 29.4348C0.384041 29.6342 -0.251028 30.7142 0.0947684 31.7277C5.49784 47.5624 9.17525 64.0716 41.198 61.5327C38.9803 50.4568 32.6761 44.9537 27.2697 41.5974C26.8208 41.3182 27.15 40.6304 27.6521 40.8032C38.4649 44.5383 49.394 55.7671 47.3326 78.5404C46.8671 78.6368 46.4116 78.7232 45.976 78.8461C37.1748 81.3783 24.8059 87.4197 22.1526 89.7459C17.3281 93.9762 31.4592 95.9501 51.5619 96C51.5985 96 51.6317 96 51.6683 96C51.778 96 51.8878 96 51.9942 96C52.1006 96 52.2136 96 52.32 96C52.3566 96 52.3898 96 52.4264 96C72.5292 95.9501 86.6603 93.9762 81.8357 89.7459C79.1824 87.4164 68.0937 81.7106 58.0124 78.8461C57.5768 78.7232 57.1213 78.6334 56.6558 78.5371C56.5959 78.3244 56.5394 78.115 56.4829 77.9057C54.0357 57.4719 57.4205 28.0059 78.9995 20.0304C79.465 19.8576 79.8175 20.4757 79.4318 20.7914C67.6348 30.4218 62.8435 41.0092 62.3415 52.547V52.5503Z"
      />
    </svg>
  ),
  leafBadge: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M62.1073 2.72603C59.6642 3.30759 57.0877 2.85779 54.9943 1.48569C51.9669 -0.495229 48.0331 -0.495229 45.0057 1.48569C42.9123 2.85779 40.3358 3.30759 37.8927 2.72603C34.3638 1.88551 30.6647 3.21672 28.5068 6.10177C27.0116 8.09632 24.7479 9.39119 22.2496 9.66833C18.6425 10.0727 15.6243 12.5716 14.5983 16.0109C13.8898 18.3916 12.2058 20.3726 9.95598 21.4766C6.70314 23.0713 4.73395 26.438 4.95939 30.0182C5.11582 32.4943 4.21864 34.9205 2.4887 36.7197C-0.0141929 39.3185 -0.699728 43.144 0.754159 46.4334C1.75716 48.7097 1.75716 51.2903 0.754159 53.5666C-0.699728 56.856 -0.0141929 60.6815 2.4887 63.2803C4.21864 65.0795 5.11582 67.5057 4.95939 69.9818C4.73395 73.562 6.70314 76.9241 9.95598 78.5234C12.2058 79.632 13.8852 81.6084 14.5983 83.9891C15.6243 87.433 18.6379 89.9273 22.2496 90.3317C24.7479 90.6088 27.0162 91.9037 28.5068 93.8982C30.6647 96.7878 34.3592 98.1145 37.8927 97.274C40.3358 96.6924 42.9123 97.1422 45.0057 98.5143C48.0331 100.495 51.9669 100.495 54.9943 98.5143C57.0877 97.1422 59.6642 96.6924 62.1073 97.274C65.6362 98.1145 69.3353 96.7833 71.4931 93.8982C72.9884 91.9037 75.2521 90.6088 77.7504 90.3317C81.3575 89.9273 84.3757 87.4284 85.4017 83.9891C86.1102 81.6084 87.7942 79.6274 90.044 78.5234C93.2969 76.9287 95.2661 73.562 95.0406 69.9818C94.8842 67.5057 95.7813 65.0795 97.5113 63.2803C100.014 60.6815 100.7 56.856 99.2458 53.5666C98.2428 51.2903 98.2428 48.7097 99.2458 46.4334C100.7 43.144 100.014 39.3185 97.5113 36.7197C95.7813 34.9205 94.8842 32.4943 95.0406 30.0182C95.2661 26.438 93.2969 23.0759 90.044 21.4766C87.7942 20.368 86.1148 18.3916 85.4017 16.0109C84.3757 12.567 81.3621 10.0727 77.7504 9.66833C75.2521 9.39119 72.9838 8.09632 71.4931 6.10177C69.3353 3.21218 65.6408 1.88551 62.1073 2.72603ZM73.5362 30.7721C72.2235 39.1698 70.5764 49.7067 55.9239 51.5088V51.5071C56.1649 45.8002 58.4647 40.5634 64.1273 35.8C64.3124 35.6439 64.1432 35.3382 63.9198 35.4236C53.5619 39.3685 51.9371 53.9431 53.1118 64.0501C53.1389 64.1537 53.166 64.2572 53.1948 64.3624C53.2193 64.3676 53.2437 64.3728 53.268 64.378C53.466 64.42 53.6598 64.4611 53.8459 64.5153C58.685 65.9321 64.0076 68.7544 65.2812 69.9066C67.5969 71.999 60.814 72.9753 51.1647 73H51.1136H50.9572H50.8008H50.7497C41.1004 72.9753 34.3175 71.999 36.6332 69.9066C37.9068 68.756 43.8439 65.7678 48.0685 64.5153C48.2434 64.4644 48.425 64.4262 48.6106 64.3871C48.6468 64.3795 48.6831 64.3718 48.7196 64.3641C49.7091 53.0999 44.4631 47.5458 39.273 45.6983C39.032 45.6129 38.874 45.9531 39.0895 46.0912C41.6845 47.7513 44.7105 50.4733 45.775 55.9517C31.2221 57.1406 28.8654 49.884 26.4547 42.4611C26.3192 42.0439 26.1835 41.6262 26.0455 41.2094C25.8795 40.7081 26.1843 40.1739 26.6903 40.0753C37.1184 38.0486 44.902 40.2577 49.5815 48.3726C50.8072 29.6855 62.6957 27.5931 73.1127 27.0014C73.6585 26.9701 74.0846 27.4748 73.9857 28.0287C73.828 28.9056 73.6842 29.8252 73.5362 30.7721Z"
      />
    </svg>
  ),
  recycle: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M32.0132 77.7899H15.471C14.2698 77.7899 13.6588 77.0265 13.4059 76.5811C13.153 76.1358 12.8158 75.2028 13.448 74.1849L20.2124 63.0943L26.5553 66.5296C27.904 67.2718 29.4634 65.9783 28.9998 64.4939L23.268 46.257C22.994 45.3664 22.0668 44.8786 21.1818 45.1119L2.86945 50.2861C1.39435 50.7102 1.16252 52.7247 2.51118 53.4457L8.70661 56.8174L2.32153 67.2718C-0.64974 72.1279 -0.776185 78.0019 1.98436 82.9852C4.74491 87.9686 9.78135 90.9374 15.45 90.9374H31.9921C35.5956 90.9374 38.5247 87.9898 38.5247 84.3636C38.5247 80.7374 35.5956 77.7899 31.9921 77.7899H32.0132Z" />
      <path d="M73.1475 36.8629L78.0153 18.3715C78.4157 16.8871 76.7931 15.6572 75.4866 16.463L69.4597 20.1104L63.7489 9.25308C61.0938 4.20613 56.1417 1.1313 50.4731 1.00407C44.8045 0.876836 39.7048 3.7396 36.8389 8.63811L28.4519 22.9732C26.6186 26.1116 27.6511 30.1407 30.7699 31.9644C31.8025 32.5793 32.9615 32.8762 34.0784 32.8762C36.3121 32.8762 38.5036 31.7099 39.7048 29.6317L48.0918 15.2967C48.7029 14.2576 49.6512 14.1304 50.178 14.1304C50.6838 14.1304 51.6532 14.3212 52.2011 15.3815L58.249 26.875L52.0957 30.6284C50.7891 31.4342 51.1053 33.4276 52.6014 33.7668L71.1245 38.0504C72.0306 38.2625 72.9157 37.7111 73.1475 36.8205V36.8629Z" />
      <path d="M97.6342 67.3143L88.8679 53.1913C86.9502 50.1165 82.9253 49.1622 79.8697 51.0919C76.8141 53.0216 75.8659 57.0719 77.7835 60.1467L86.5498 74.2697C87.182 75.2876 86.8659 76.2206 86.613 76.666C86.3602 77.1113 85.7701 77.8959 84.569 77.8959L71.6303 77.9807L71.1877 70.7496C71.0823 69.2016 69.1858 68.5654 68.1954 69.7317L55.8466 84.2788C55.2566 84.9786 55.3198 86.0389 55.9942 86.6538L70.0498 99.5469C71.1877 100.586 72.9999 99.7166 72.8946 98.1685L72.4521 91.107L84.6533 91.0222C90.3219 90.9798 95.3372 87.9898 98.0556 82.9853C100.795 77.9807 100.627 72.128 97.6342 67.2931V67.3143Z" />
    </svg>
  ),
  menu: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 stroke-secondary", props.className)}
      viewBox="0 0 18 8"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M1 1H17" strokeWidth="2" />
      <path d="M1 7H17" strokeWidth="2" />
    </svg>
  ),
  quote: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 56 39"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.2446 39L21.0588 24.0118C23.4631 19.2706 24.515 16.0588 24.515 12.2353C24.515 5.04707 19.2555 0 12.1926 0C5.58062 0 0.0205078 5.04707 0.0205078 12.2353C0.0205078 17.8941 4.5287 22.4824 9.93854 22.4824H10.2391L1.22269 39H13.2446ZM43.75 39L51.5642 24.0118C53.9686 19.2706 55.0205 16.0588 55.0205 12.2353C55.0205 5.04707 49.7609 0 42.6981 0C36.0861 0 30.526 5.04707 30.526 12.2353C30.526 17.8941 35.0342 22.4824 40.444 22.4824H40.7445L31.7282 39H43.75Z"
        fill="#002652"
      />
    </svg>
  ),
  filledStar: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 41 38"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M40.5 14.5225H25.2254L20.5074 0L15.7746 14.5225L0.5 14.5078L12.8701 23.4922L8.1373 38L20.5074 29.0302L32.8627 38L28.1447 23.4922L40.5 14.5225Z"
        fill="#00B67A"
      />
      <path d="M29.2065 26.7767L28.1447 23.4922L20.5074 29.0302L29.2065 26.7767Z" fill="#005128" />
    </svg>
  ),
  halfFilledStar: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      width="41"
      height="38"
      viewBox="0 0 41 38"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M40.5 14.5225H25.2254L20.5074 0L15.7746 14.5225L0.5 14.5078L12.8701 23.4922L8.1373 38L20.5074 29.0302L32.8627 38L28.1447 23.4922L40.5 14.5225Z"
        fill="#00B67A"
      />
      <path d="M29.2065 26.7767L28.1447 23.4922L20.5074 29.0302L29.2065 26.7767Z" fill="#005128" />
      <mask id="mask0_1445_2509" maskUnits="userSpaceOnUse" x="20" y="0" width="21" height="38">
        <rect x="20.5" width="20" height="38" fill="#D9D9D9" />
      </mask>
      <g mask="url(#mask0_1445_2509)">
        <path
          d="M40.5 14.5225H25.2254L20.5074 0L15.7746 14.5225L0.5 14.5078L12.8701 23.4922L8.1373 38L20.5074 29.0302L32.8627 38L28.1447 23.4922L40.5 14.5225Z"
          fill="#CCCCCC"
        />
        <path d="M29.2065 26.7767L28.1447 23.4922L20.5074 29.0302L29.2065 26.7767Z" fill="#808080" />
      </g>
    </svg>
  ),
  minus: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 stroke-secondary", props.className)}
      viewBox="0 0 12 3"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M11 1.5L1 1.5" strokeWidth="2" />
    </svg>
  ),
  plus: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 stroke-secondary", props.className)}
      viewBox="0 0 12 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M6 1.5V11.5" strokeWidth="2" />
      <path d="M11 6.5L1 6.5" strokeWidth="2" />
    </svg>
  ),
  x: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 stroke-secondary", props.className)}
      viewBox="0 0 16 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M2.34375 1.34375L13.6575 12.6575" strokeWidth="2" />
      <path d="M2.34375 12.6582L13.6575 1.34449" strokeWidth="2" />
    </svg>
  ),
  chevronDown: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      width="12"
      height="9"
      viewBox="0 0 12 9"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M2 2L6 6L10 2" strokeWidth="3" />
    </svg>
  ),
  copy: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.5 0C4.11929 0 3 1.11929 3 2.5V3H2.5C1.11929 3 0 4.11929 0 5.5V13.5C0 14.8807 1.11929 16 2.5 16H10.5C11.8807 16 13 14.8807 13 13.5V13H13.5C14.8807 13 16 11.8807 16 10.5V2.5C16 1.11929 14.8807 0 13.5 0H5.5ZM12 13H5.5C4.11929 13 3 11.8807 3 10.5V4H2.5C1.67157 4 1 4.67157 1 5.5V13.5C1 14.3284 1.67157 15 2.5 15H10.5C11.3284 15 12 14.3284 12 13.5V13ZM4 2.5C4 1.67157 4.67157 1 5.5 1H13.5C14.3284 1 15 1.67157 15 2.5V10.5C15 11.3284 14.3284 12 13.5 12H5.5C4.67157 12 4 11.3284 4 10.5V2.5Z"
      />
    </svg>
  ),
  facebook: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 36 36"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M36 18.1096C36 8.1069 27.9422 0 18 0C8.05781 0 0 8.1069 0 18.1096C0 27.1503 6.58125 34.6418 15.1875 36V23.3445H10.6172V18.1096H15.1875V14.1199C15.1875 9.58184 17.8734 7.07408 21.9867 7.07408C23.9555 7.07408 26.0156 7.42779 26.0156 7.42779V11.8845H23.7445C21.5086 11.8845 20.8125 13.2816 20.8125 14.7141V18.1096H25.8047L25.0066 23.3445H20.8125V36C29.4188 34.6418 36 27.1503 36 18.1096Z" />
    </svg>
  ),
  instagram: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 20 20"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M10.0009 0C7.28508 0 6.94424 0.0118752 5.87756 0.0604169C4.81297 0.109167 4.08629 0.277708 3.45045 0.525C2.79274 0.780417 2.23481 1.12208 1.67898 1.67813C1.12272 2.23396 0.78105 2.79187 0.524797 3.44937C0.276878 4.08542 0.108126 4.81229 0.0602089 5.87646C0.0125 6.94312 0 7.28417 0 10C0 12.7158 0.0120837 13.0556 0.0604175 14.1223C0.109376 15.1869 0.27792 15.9135 0.525006 16.5494C0.780633 17.2071 1.1223 17.765 1.67835 18.3208C2.23398 18.8771 2.7919 19.2196 3.4492 19.475C4.08546 19.7223 4.81234 19.8908 5.87673 19.9396C6.94341 19.9881 7.28403 20 9.99969 20C12.7158 20 13.0556 19.9881 14.1222 19.9396C15.1868 19.8908 15.9143 19.7223 16.5506 19.475C17.2081 19.2196 17.7652 18.8771 18.3208 18.3208C18.8771 17.765 19.2187 17.2071 19.475 16.5496C19.7208 15.9135 19.8896 15.1867 19.9396 14.1225C19.9875 13.0558 20 12.7158 20 10C20 7.28417 19.9875 6.94333 19.9396 5.87667C19.8896 4.81208 19.7208 4.08542 19.475 3.44958C19.2187 2.79187 18.8771 2.23396 18.3208 1.67813C17.7646 1.12188 17.2083 0.780208 16.55 0.525C15.9125 0.277708 15.1854 0.109167 14.1208 0.0604169C13.0541 0.0118752 12.7145 0 9.99781 0H10.0009ZM9.10385 1.80208C9.3701 1.80167 9.66718 1.80208 10.0009 1.80208C12.671 1.80208 12.9874 1.81167 14.0418 1.85958C15.0168 1.90417 15.546 2.06708 15.8985 2.20396C16.3652 2.38521 16.6979 2.60187 17.0477 2.95187C17.3977 3.30187 17.6143 3.63521 17.796 4.10187C17.9329 4.45396 18.096 4.98312 18.1404 5.95812C18.1883 7.01229 18.1987 7.32896 18.1987 9.99771C18.1987 12.6665 18.1883 12.9831 18.1404 14.0373C18.0958 15.0123 17.9329 15.5415 17.796 15.8935C17.6148 16.3602 17.3977 16.6925 17.0477 17.0423C16.6977 17.3923 16.3654 17.609 15.8985 17.7902C15.5464 17.9277 15.0168 18.0902 14.0418 18.1348C12.9876 18.1827 12.671 18.1931 10.0009 18.1931C7.3307 18.1931 7.01424 18.1827 5.96006 18.1348C4.98505 18.0898 4.45588 17.9269 4.10317 17.79C3.6365 17.6087 3.30316 17.3921 2.95316 17.0421C2.60315 16.6921 2.38648 16.3596 2.20481 15.8927C2.06794 15.5406 1.90481 15.0115 1.86044 14.0365C1.81252 12.9823 1.80294 12.6656 1.80294 9.99521C1.80294 7.32479 1.81252 7.00979 1.86044 5.95563C1.90502 4.98063 2.06794 4.45146 2.20481 4.09896C2.38607 3.63229 2.60315 3.29896 2.95316 2.94896C3.30316 2.59896 3.6365 2.38229 4.10317 2.20062C4.45567 2.06312 4.98505 1.90063 5.96006 1.85583C6.88257 1.81417 7.24008 1.80167 9.10385 1.79958V1.80208ZM15.3389 3.4625C14.6764 3.4625 14.1389 3.99937 14.1389 4.66208C14.1389 5.32458 14.6764 5.86208 15.3389 5.86208C16.0014 5.86208 16.5389 5.32458 16.5389 4.66208C16.5389 3.99958 16.0014 3.46208 15.3389 3.46208V3.4625ZM10.0009 4.86458C7.16487 4.86458 4.86547 7.16396 4.86547 10C4.86547 12.836 7.16487 15.1344 10.0009 15.1344C12.837 15.1344 15.1356 12.836 15.1356 10C15.1356 7.16396 12.837 4.86458 10.0009 4.86458ZM10.0009 6.66667C11.8418 6.66667 13.3343 8.15896 13.3343 10C13.3343 11.8408 11.8418 13.3333 10.0009 13.3333C8.15988 13.3333 6.66757 11.8408 6.66757 10C6.66757 8.15896 8.15988 6.66667 10.0009 6.66667Z" />
    </svg>
  ),
  linkedin: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 20 20"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.31348 17.9444V5.81694H0.249539V17.9444H4.31348ZM2.28151 4.16C3.70746 4.16 4.59867 3.24339 4.59867 2.08C4.56302 0.881355 3.70746 0 2.31716 0C0.926864 0 0 0.91661 0 2.08C0 3.20813 0.855567 4.16 2.28151 4.16Z"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.55863 17.9444H10.6226V11.1756C10.6226 10.8231 10.6582 10.4353 10.7652 10.1885C11.0504 9.44818 11.7277 8.70784 12.8684 8.70784C14.33 8.70784 14.9361 9.83598 14.9361 11.4577V17.9444H19V10.9994C19 7.26242 17.0037 5.53496 14.2944 5.53496C12.0842 5.53496 11.1217 6.76886 10.5869 7.57971H10.6226V5.817H6.55863C6.62993 6.94513 6.55863 17.9444 6.55863 17.9444Z"
      />
    </svg>
  ),
  euStars: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 55 55"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_11208_616592)">
        <path
          d="M30.3423 49.6615C30.1177 49.512 30.0429 49.2131 30.1177 48.9888L30.492 47.4191C30.6417 46.8212 29.968 46.3727 29.4441 46.6717L28.0968 47.4939C27.8722 47.6434 27.5728 47.6434 27.3482 47.4939L26.0009 46.6717C25.477 46.3727 24.8033 46.8212 24.953 47.4191L25.3273 48.9888C25.4021 49.2131 25.3273 49.512 25.1027 49.6615L23.9051 50.708C23.456 51.0817 23.6805 51.8292 24.2794 51.9039L25.8512 52.0534C26.0758 52.0534 26.3003 52.2029 26.45 52.5019L27.0488 53.9968C27.2734 54.5948 28.0968 54.5948 28.3213 53.9968L28.9201 52.5019C28.995 52.2777 29.2195 52.1282 29.5189 52.0534L31.0908 51.9039C31.6896 51.8292 31.9142 51.0817 31.4651 50.708L30.3409 49.6615L30.3423 49.6615Z"
          fill="#A9C8FF"
        />
        <path
          d="M42.5428 45.7017L40.9709 45.4027C40.7464 45.328 40.5218 45.1785 40.447 44.9543L39.9979 43.4593C39.8482 42.8614 39.0248 42.7866 38.7254 43.3098L37.9769 44.73C37.8272 44.9543 37.6026 45.1037 37.3781 45.1037L35.8062 45.029C35.2074 45.029 34.908 45.7765 35.2823 46.1502L36.405 47.3461C36.5547 47.4956 36.6296 47.7946 36.5547 48.0189L36.0308 49.5138C35.8062 50.1118 36.405 50.635 37.0038 50.336L38.426 49.6633C38.6506 49.5885 38.95 49.5885 39.0997 49.738L40.3721 50.7097C40.8212 51.0835 41.5698 50.7097 41.4949 50.1118L41.2704 48.5421C41.2704 48.3178 41.3452 48.0189 41.5698 47.8694L42.9171 46.9724C43.294 46.5987 43.1443 45.8499 42.5442 45.7017L42.5428 45.7017Z"
          fill="#A9C8FF"
        />
        <path
          d="M19.713 45.029L18.1411 45.1037C17.9165 45.1037 17.6171 44.9543 17.5423 44.73L16.7938 43.3098C16.4944 42.7866 15.7459 42.8614 15.5213 43.4593L15.0722 44.9543C14.9973 45.1785 14.7728 45.4027 14.5482 45.4027L12.9764 45.7017C12.3776 45.8512 12.2279 46.5987 12.677 46.9724L13.9508 47.8694C14.1753 48.0189 14.2502 48.2431 14.2502 48.5421L14.0256 50.1118C13.9508 50.7097 14.6244 51.0835 15.1484 50.7097L16.4209 49.738C16.6454 49.5885 16.87 49.5885 17.1694 49.6633L18.5915 50.336C19.1155 50.5602 19.7143 50.037 19.5646 49.5138L19.0406 48.0189C18.9658 47.7946 19.0406 47.4956 19.1903 47.3461L20.2383 46.1502C20.6125 45.7017 20.3131 44.9543 19.7143 45.029L19.713 45.029Z"
          fill="#A9C8FF"
        />
        <path
          d="M12.0783 38.6724L10.6562 37.925C10.4316 37.7755 10.2819 37.5512 10.2819 37.327L10.2819 35.7573C10.2819 35.1594 9.53339 34.8604 9.15913 35.2341L8.03636 36.3553C7.81181 36.5048 7.58726 36.5795 7.36271 36.5048L5.86568 35.9816C5.26687 35.7573 4.74292 36.3553 5.04232 36.9533L5.71598 38.3735C5.79083 38.5977 5.79083 38.8967 5.64113 39.0462L4.66807 40.3169C4.29381 40.7653 4.66806 41.5128 5.26687 41.4381L6.83875 41.1404C7.0633 41.1404 7.3627 41.2152 7.51241 41.4394L8.41062 42.7848C8.78488 43.3081 9.53338 43.0838 9.68309 42.4859L9.98249 40.9162C10.0573 40.6919 10.207 40.4677 10.4316 40.3929L11.9286 39.9445C12.5274 39.795 12.6023 38.9728 12.0783 38.6738L12.0783 38.6724Z"
          fill="#A9C8FF"
        />
        <path
          d="M49.8038 38.9707C49.6541 38.7464 49.6541 38.5222 49.729 38.2979L50.4027 36.8778C50.6272 36.3545 50.1033 35.7566 49.5793 35.906L48.0823 36.4293C47.8577 36.504 47.5583 36.4293 47.4086 36.2798L46.2858 35.1586C45.8367 34.7101 45.0882 35.0838 45.1631 35.6818L45.2379 37.2515C45.2379 37.4757 45.0882 37.7747 44.8637 37.8495L43.4415 38.5969C42.9175 38.8959 42.9924 39.6434 43.5912 39.8676L45.0882 40.3161C45.3128 40.3908 45.5373 40.6151 45.5373 40.8393L45.8367 42.409C45.9864 43.007 46.735 43.1565 47.1092 42.708L47.9326 41.436C48.0823 41.2117 48.3068 41.137 48.6062 41.137L50.1781 41.3612C50.7769 41.436 51.1512 40.7632 50.7769 40.24L49.8038 38.9693L49.8038 38.9707Z"
          fill="#A9C8FF"
        />
        <path
          d="M52.4982 26.4148C52.2737 26.3401 52.124 26.1159 52.0491 25.8169L51.8994 24.2472C51.8245 23.6492 51.076 23.425 50.7018 23.8735L49.6539 25.0694C49.5042 25.2936 49.2048 25.3684 48.9802 25.2936L47.4083 24.9199C46.8095 24.7704 46.3604 25.4431 46.6598 25.9664L47.4832 27.3118C47.6329 27.536 47.6329 27.835 47.4832 28.0593L46.6598 29.4047C46.3604 29.9279 46.8095 30.6007 47.4083 30.4512L48.9802 30.0774C49.2048 30.0027 49.5042 30.0774 49.6539 30.3017L50.7018 31.4976C51.076 31.9461 51.8245 31.7218 51.8994 31.1239L52.0491 29.5542C52.0491 29.33 52.1988 29.1057 52.4982 28.9562L53.9952 28.3583C54.594 28.134 54.594 27.3118 53.9952 27.0876L52.4982 26.4162L52.4982 26.4148Z"
          fill="#A9C8FF"
        />
        <path
          d="M5.71632 30.3022C5.86602 30.078 6.16543 30.0032 6.38998 30.078L7.96185 30.4517C8.56066 30.6012 9.00977 29.9285 8.71036 29.4052L7.887 28.0598C7.7373 27.8356 7.7373 27.5366 7.887 27.3123L8.71036 25.9669C9.00977 25.4437 8.56066 24.7709 7.96185 24.9204L6.38998 25.2942C6.16543 25.3689 5.86602 25.2942 5.71632 25.0699L4.66841 23.874C4.29415 23.4255 3.54564 23.6498 3.47079 24.2477L3.32109 25.8174C3.32108 26.0416 3.17138 26.2659 2.87198 26.4154L1.37496 27.0133C0.776148 27.2376 0.776148 28.0598 1.37496 28.284L2.87198 28.882C3.09653 28.9568 3.24623 29.181 3.32108 29.48L3.47079 31.0497C3.54564 31.6476 4.29415 31.8719 4.66841 31.4234L5.71632 30.3009L5.71632 30.3022Z"
          fill="#A9C8FF"
        />
        <path
          d="M5.64113 16.4002C5.79083 16.6244 5.79083 16.8487 5.71598 17.0729L5.04232 18.4931C4.81777 19.0163 5.34172 19.6143 5.86568 19.4648L7.36271 18.9416C7.58726 18.8668 7.88666 18.9416 8.03636 19.0911L9.23398 20.2123C9.68309 20.6608 10.4316 20.287 10.3567 19.6891L10.2819 18.1194C10.2819 17.8951 10.4316 17.5962 10.6562 17.5214L12.0783 16.7739C12.6023 16.475 12.5274 15.7275 11.9286 15.5033L10.4316 15.0548C10.207 14.98 9.98249 14.7558 9.98249 14.5315L9.68309 12.9619C9.60824 12.3639 8.78488 12.2144 8.41062 12.6629L7.51241 14.0083C7.36271 14.2326 7.13815 14.3073 6.83875 14.3073L5.26687 14.0831C4.66806 14.0083 4.29381 14.681 4.66807 15.2043L5.64113 16.4015L5.64113 16.4002Z"
          fill="#A9C8FF"
        />
        <path
          d="M43.3663 16.6976L44.7885 17.4451C45.013 17.5946 45.1627 17.8188 45.1627 18.043L45.0879 19.6127C45.0879 20.2107 45.8364 20.5097 46.2107 20.1359L47.3334 19.0147C47.4831 18.8653 47.7825 18.7905 48.0071 18.8653L49.5041 19.3885C50.1029 19.6127 50.6269 19.0147 50.3275 18.4168L49.6538 16.9966C49.579 16.7724 49.579 16.4734 49.7287 16.3239L50.7017 15.0532C51.076 14.6047 50.7017 13.8572 50.1029 13.932L48.531 14.1562C48.3065 14.1562 48.0071 14.0815 47.8574 13.8572L46.9592 12.5118C46.5849 11.9886 45.8364 12.2128 45.6867 12.8108L45.3873 14.3805C45.3124 14.6047 45.1627 14.8289 44.9382 14.9037L43.4412 15.3522C42.9172 15.5751 42.8424 16.3973 43.3663 16.6963L43.3663 16.6976Z"
          fill="#A9C8FF"
        />
        <path
          d="M35.7312 10.345L37.3031 10.2702C37.5276 10.2702 37.827 10.4197 37.9019 10.644L38.6504 12.0642C38.9498 12.5874 39.7731 12.5126 39.9228 11.9147L40.372 10.4197C40.4468 10.1955 40.6714 9.97126 40.8959 9.97126L42.4678 9.67227C43.0666 9.52278 43.2163 8.77532 42.7672 8.40159L41.4199 7.50463C41.1953 7.35513 41.1205 7.13089 41.1205 6.83191L41.4185 5.2609C41.4934 4.66292 40.8197 4.28919 40.2958 4.66292L39.0233 5.63463C38.7987 5.78412 38.5742 5.78412 38.3496 5.70937L36.9275 5.03665C36.4035 4.81241 35.8047 5.33564 35.9544 5.85886L36.4784 7.3538C36.5532 7.57804 36.4784 7.87702 36.3287 8.02652L35.2059 9.22246C34.8316 9.67094 35.131 10.4184 35.7299 10.3437L35.7312 10.345Z"
          fill="#A9C8FF"
        />
        <path
          d="M19.1142 8.02785C18.9645 7.87836 18.8896 7.57937 18.9645 7.35513L19.4884 5.8602C19.713 5.26223 19.1142 4.739 18.5154 5.03799L17.0932 5.71071C16.8686 5.78546 16.5692 5.78546 16.3447 5.63596L15.0722 4.66426C14.6231 4.29053 13.8746 4.66426 13.9494 5.26223L14.2488 6.83191C14.2488 7.05615 14.174 7.35513 13.9494 7.50463L12.6021 8.40158C12.0782 8.77532 12.2279 9.52278 12.9015 9.67227L14.4734 9.97126C14.698 10.046 14.9225 10.1955 14.9974 10.4197L15.4465 11.9147C15.5962 12.5126 16.4195 12.5874 16.7189 12.0642L17.4674 10.644C17.6171 10.4197 17.8417 10.2702 18.0663 10.2702L19.6381 10.345C20.2369 10.345 20.5363 9.59753 20.1621 9.2238L19.1128 8.02785L19.1142 8.02785Z"
          fill="#A9C8FF"
        />
        <path
          d="M31.1651 3.46945L29.5933 3.31996C29.3687 3.31996 29.1442 3.17046 28.9945 2.87148L28.3956 1.37655C28.1711 0.778573 27.3477 0.778573 27.1232 1.37655L26.5244 2.87148C26.4495 3.09572 26.225 3.24521 25.9256 3.31996L24.3537 3.46945C23.7549 3.54419 23.5303 4.29166 23.9794 4.6654L25.177 5.71185C25.4016 5.86134 25.4765 6.16033 25.4016 6.38457L25.0273 7.95424C24.8776 8.55221 25.5513 9.00069 26.0753 8.70171L27.4226 7.87949C27.6471 7.73 27.9465 7.73 28.1711 7.8795L29.5184 8.70171C30.0424 9.00069 30.716 8.55221 30.5663 7.95424L30.1921 6.38457C30.1172 6.16033 30.1921 5.86134 30.4166 5.71185L31.6142 4.6654C31.9898 4.29166 31.7639 3.54419 31.1665 3.46945L31.1651 3.46945Z"
          fill="#A9C8FF"
        />
      </g>
      <defs>
        <clipPath id="clip0_11208_616592">
          <rect width="53.5185" height="53.5185" fill="white" transform="translate(54.4443 54.4453) rotate(-180)" />
        </clipPath>
      </defs>
    </svg>
  ),
  circleOutlineChevronleft: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5", props.className)}
      viewBox="0 0 56 56"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect x="1" y="1" width="54" height="54" rx="27" stroke="#002652" strokeWidth="2" />
      <path
        d="M30.4996 31.2292L27.2663 27.9958L30.4996 24.7625C30.8246 24.4375 30.8246 23.9125 30.4996 23.5875C30.1746 23.2625 29.6496 23.2625 29.3246 23.5875L25.4996 27.4125C25.1746 27.7375 25.1746 28.2625 25.4996 28.5875L29.3246 32.4125C29.6496 32.7375 30.1746 32.7375 30.4996 32.4125C30.8163 32.0875 30.8246 31.5542 30.4996 31.2292Z"
        fill="#002652"
      />
    </svg>
  ),
  awardBadge: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5", props.className)}
      viewBox="0 0 24 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M23.6618 12.5907C23.4244 12.0565 23.4244 11.4483 23.6618 10.9141C24.0056 10.141 23.8438 9.24173 23.2509 8.63036C22.8411 8.20783 22.6292 7.63715 22.6654 7.05499C22.7187 6.21306 22.2524 5.42224 21.4828 5.04666C20.9495 4.78688 20.5514 4.32157 20.3833 3.76237C20.1395 2.95382 19.4263 2.36645 18.5716 2.27151C17.9808 2.20578 17.4432 1.90218 17.0898 1.43374C16.5789 0.755603 15.7029 0.442616 14.8673 0.640842C14.2893 0.777513 13.6783 0.67214 13.1822 0.349763C12.4659 -0.116588 11.5334 -0.116588 10.817 0.349763C10.321 0.67214 9.71105 0.777513 9.13305 0.640842C8.2964 0.44366 7.42141 0.755603 6.91048 1.43374C6.55708 1.90322 6.0206 2.20682 5.42876 2.27151C4.574 2.36645 3.85976 2.95277 3.61706 3.76237C3.44888 4.32157 3.05077 4.78688 2.51748 5.04666C1.74682 5.42224 1.28165 6.21306 1.33488 7.05499C1.37213 7.63715 1.16031 8.20783 0.749427 8.63036C0.156527 9.24173 -0.00527014 10.1421 0.338548 10.9141C0.575921 11.4483 0.575921 12.0565 0.338548 12.5907C-0.00527014 13.3638 0.156527 14.2631 0.749427 14.8745C1.15924 15.297 1.37107 15.8677 1.33488 16.4498C1.28165 17.2918 1.74788 18.0826 2.51748 18.4582C3.05077 18.7179 3.44888 19.1833 3.61706 19.7425C3.86082 20.551 4.574 21.1384 5.42876 21.2333C6.01953 21.299 6.55708 21.6026 6.91048 22.0721C7.42141 22.7503 8.29746 23.0633 9.13305 22.865C9.71105 22.7284 10.322 22.8337 10.817 23.1561C11.5334 23.6225 12.4659 23.6225 13.1822 23.1561C13.6783 22.8337 14.2882 22.7284 14.8673 22.865C15.7039 23.0622 16.5789 22.7503 17.0898 22.0721C17.4432 21.6026 17.9797 21.299 18.5716 21.2333C19.4263 21.1384 20.1406 20.5521 20.3833 19.7425C20.5514 19.1833 20.9495 18.7179 21.4828 18.4582C22.2535 18.0826 22.7187 17.2918 22.6654 16.4498C22.6282 15.8677 22.84 15.297 23.2509 14.8745C23.8438 14.2631 24.0056 13.3638 23.6618 12.5907ZM18.6961 10.4384L15.9232 13.0873L16.5778 16.8285C16.6885 17.4629 16.0094 17.947 15.4282 17.6475L12.0007 15.8812L8.57315 17.6475C7.99196 17.947 7.31284 17.4629 7.42354 16.8285L8.07818 13.0873L5.30528 10.4384C4.8348 9.98873 5.09452 9.20626 5.7449 9.11341L9.57693 8.56777L11.2907 5.16455C11.5813 4.58761 12.4211 4.58761 12.7117 5.16455L14.4255 8.56777L18.2575 9.11341C18.9079 9.20626 19.1666 9.98873 18.6972 10.4384H18.6961Z"
        fill="#A9C8FF"
      />
      <path
        d="M9.38234 23.8788C8.12735 24.1751 6.82127 23.7098 6.0538 22.6915C5.8771 22.4568 5.60566 22.3034 5.30974 22.27C4.7392 22.2074 4.21336 21.9988 3.77161 21.6806L0.363233 28.7624C0.18547 29.1318 0.487774 29.5501 0.902911 29.5073L3.72052 29.2184C3.9185 29.1985 4.11224 29.2872 4.22187 29.45L5.78662 31.765C6.01654 32.1062 6.53919 32.0686 6.71695 31.6993L10.3691 24.1094L10.3517 24.0993C10.3104 24.0755 10.2687 24.0514 10.2286 24.0249C9.98056 23.8631 9.67187 23.8099 9.38234 23.8788Z"
        fill="#A9C8FF"
      />
      <path
        d="M14.6185 23.8783C15.8735 24.1746 17.1796 23.7093 17.947 22.691L17.9481 22.69C18.1248 22.4553 18.3962 22.3009 18.6921 22.2685C19.2616 22.2059 19.7885 21.9973 20.2303 21.6791L23.6376 28.762C23.8154 29.1313 23.5131 29.5496 23.0979 29.5069L20.2803 29.2179C20.0823 29.197 19.8886 29.2867 19.7789 29.4495L18.2142 31.7645C17.9832 32.1057 17.4616 32.0681 17.2839 31.6988L13.6317 24.1089C13.6473 24.0995 13.6631 24.0903 13.6788 24.0812C13.7103 24.0629 13.7417 24.0445 13.7722 24.0244C14.0203 23.8627 14.329 23.8095 14.6185 23.8783Z"
        fill="#A9C8FF"
      />
    </svg>
  ),
  globeIcon: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5", props.className)}
      xmlns="http://www.w3.org/2000/svg"
      width="36"
      height="36"
      viewBox="0 0 36 36"
      fill="none"
    >
      <g clipPath="url(#clip0_15522_14579)">
        <path
          d="M24.7524 4.46115C24.1573 3.18907 23.4851 2.11537 22.7517 1.25223V1.25397C22.5434 1.0086 22.7815 0.644904 23.0913 0.733653C25.0044 1.28878 26.788 2.14843 28.3826 3.25867C28.6014 3.41181 28.5541 3.74941 28.2986 3.8312C27.3254 4.14791 26.2612 4.42286 25.1147 4.64735C24.9659 4.67519 24.8154 4.59688 24.7524 4.46115Z"
          fill="#002652"
        />
        <path
          d="M13.2916 1.25453C12.5599 2.11245 11.8895 3.18267 11.2962 4.44779V4.44953C11.2332 4.58526 11.0826 4.66357 10.9339 4.63399C9.79438 4.40776 8.73542 4.13107 7.76923 3.81436C7.51543 3.73083 7.46642 3.39497 7.68696 3.24183C9.27453 2.14203 11.0476 1.2876 12.9502 0.735956C13.2601 0.645466 13.4999 1.00917 13.2916 1.25453Z"
          fill="#002652"
        />
        <path
          d="M14.0287 4.74699C15.2221 2.54785 16.6536 1.29672 18.0203 1.29619C19.3888 1.29672 20.8237 2.55134 22.0172 4.75569C22.1012 4.91057 22.0015 5.10373 21.8247 5.12113C20.6292 5.23599 19.4005 5.29863 18.0614 5.29863C16.7224 5.29863 15.4394 5.23251 14.2194 5.11243C14.0427 5.09503 13.9446 4.90187 14.0287 4.74699Z"
          fill="#002652"
        />
        <path d="M18.0203 1.29619L18.0212 1.29619H18.0194L18.0203 1.29619Z" fill="#002652" />
        <path
          d="M23.5252 8.5802C23.3502 7.98157 22.7708 7.59525 22.1477 7.6579C20.8139 7.79015 19.4399 7.85976 18.0431 7.85976C16.6463 7.85976 15.2181 7.78841 13.8598 7.65094C13.2349 7.58829 12.6556 7.97287 12.4805 8.57324C11.8451 10.7502 11.4058 13.3361 11.278 16.2214C11.2658 16.4946 11.4881 16.7208 11.7611 16.7208H24.2446C24.5194 16.7208 24.7399 16.4929 24.7277 16.2214C24.5981 13.3396 24.1606 10.7572 23.5269 8.58194L23.5252 8.5802Z"
          fill="#002652"
        />
        <path
          d="M14.1786 30.848C15.4056 30.7262 16.6956 30.66 18.0433 30.66L18.0416 30.6618C19.3613 30.6618 20.6251 30.7244 21.8276 30.841C22.0044 30.8584 22.1041 31.0498 22.0201 31.2065C20.8211 33.4356 19.3753 34.7042 18.0013 34.7042C16.6273 34.7042 15.185 33.4374 13.9878 31.2134C13.9038 31.0568 14.0018 30.8654 14.1786 30.848Z"
          fill="#002652"
        />
        <path
          d="M13.2693 34.7478C12.5324 33.8812 11.855 32.7988 11.2582 31.5162C11.1934 31.3788 11.0446 31.3004 10.8941 31.33C9.74588 31.558 8.67992 31.8382 7.70673 32.1584C7.45293 32.2419 7.40567 32.5777 7.62446 32.7309C9.22253 33.8463 11.0096 34.7095 12.9298 35.2663C13.2396 35.3551 13.4776 34.9914 13.2693 34.7478Z"
          fill="#002652"
        />
        <path
          d="M11.7926 19.2809H24.2113L24.2095 19.2826C24.5019 19.2826 24.7382 19.5245 24.7241 19.8151C24.5946 22.6656 24.1623 25.2219 23.5374 27.3798C23.3641 27.9801 22.783 28.3664 22.1581 28.3038C20.8209 28.1698 19.4714 28.1002 18.0431 28.1002C16.6148 28.1002 15.211 28.1715 13.8493 28.3108C13.2244 28.3734 12.6433 27.9871 12.4682 27.3867C11.8399 25.2271 11.4075 22.6673 11.278 19.8134C11.2658 19.5228 11.5003 19.2809 11.7926 19.2809Z"
          fill="#002652"
        />
        <path
          d="M24.7505 31.5008C24.1502 32.7886 23.4728 33.8762 22.7324 34.7463C22.5241 34.9899 22.7639 35.3553 23.072 35.2649C25.0026 34.7045 26.7984 33.8344 28.4035 32.7102C28.6223 32.5571 28.5733 32.2213 28.3195 32.1377C27.341 31.8193 26.2681 31.5391 25.1129 31.3129C24.9641 31.2833 24.8136 31.3616 24.7505 31.4991V31.5008Z"
          fill="#002652"
        />
        <path
          d="M27.3037 16.2581C27.1881 13.4982 26.8013 10.8601 26.1712 8.49513L26.1729 8.49687C25.9751 7.75207 26.4197 6.99509 27.1706 6.80889C28.2331 6.54612 29.2448 6.23462 30.19 5.87962C30.7291 5.67602 31.3382 5.8396 31.7145 6.27465C34.0687 8.99283 35.6178 12.4193 35.9976 16.1885C36.0256 16.4722 35.8033 16.721 35.5163 16.721H27.785C27.526 16.721 27.3142 16.5157 27.3037 16.2581Z"
          fill="#002652"
        />
        <path
          d="M27.3031 19.7437C27.1893 22.4897 26.806 25.1139 26.1811 27.4684C25.9833 28.2115 26.4279 28.9685 27.1788 29.1547C28.2483 29.4192 29.2635 29.7324 30.2139 30.0909C30.7548 30.2945 31.3656 30.1309 31.742 29.6941C34.0804 26.9811 35.619 23.5669 35.997 19.8133C36.025 19.5279 35.8027 19.2808 35.5157 19.2808H27.7844C27.5254 19.2808 27.3136 19.4861 27.3031 19.7437Z"
          fill="#002652"
        />
        <path
          d="M8.69725 19.7437C8.81277 22.4967 9.19785 25.1278 9.82447 27.4875L9.82272 27.4858C10.0188 28.2271 9.57592 28.9841 8.82853 29.1721C7.76607 29.4383 6.75437 29.7515 5.80918 30.1118C5.26833 30.3171 4.65746 30.1553 4.27938 29.7185C1.93042 27.002 0.38312 23.5791 0.00329524 19.8133C-0.0247102 19.5296 0.197583 19.2808 0.48464 19.2808H8.21591C8.47496 19.2808 8.68675 19.4861 8.69725 19.7437Z"
          fill="#002652"
        />
        <path
          d="M8.69809 16.2562C8.81361 13.4893 9.20219 10.8442 9.83406 8.4758C10.0319 7.73448 9.58902 6.97575 8.83987 6.78955C7.78266 6.52504 6.77621 6.21355 5.83628 5.85681C5.29542 5.65146 4.6863 5.8133 4.30823 6.24835C1.94176 8.97001 0.383958 12.4069 0.00238365 16.1866C-0.0256218 16.472 0.196672 16.7191 0.483728 16.7191H8.21499C8.47405 16.7191 8.68584 16.5138 8.69634 16.2562H8.69809Z"
          fill="#002652"
        />
      </g>
      <defs>
        <clipPath id="clip0_15522_14579">
          <rect width="36" height="36" fill="white" />
        </clipPath>
      </defs>
    </svg>
  ),
  starIcon: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5", props.className)}
      xmlns="http://www.w3.org/2000/svg"
      width="36"
      height="36"
      viewBox="0 0 36 36"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M31.119 14.1645C30.852 13.5636 30.852 12.8793 31.119 12.2784C31.5058 11.4087 31.3238 10.3969 30.6568 9.70916C30.1957 9.23381 29.9574 8.59179 29.9981 7.93687C30.058 6.98969 29.5335 6.10003 28.6677 5.67749C28.0677 5.38524 27.6199 4.86177 27.4307 4.23266C27.1564 3.32305 26.3541 2.66225 25.3925 2.55544C24.7279 2.4815 24.1231 2.13995 23.7256 1.61296C23.1508 0.850054 22.1652 0.497943 21.2252 0.720947C20.5749 0.874702 19.8876 0.756158 19.3295 0.393484C18.5236 -0.131161 17.4746 -0.131161 16.6687 0.393484C16.1106 0.756158 15.4244 0.874702 14.7742 0.720947C13.833 0.499117 12.8486 0.850054 12.2738 1.61296C11.8762 2.14113 11.2727 2.48267 10.6069 2.55544C9.64527 2.66225 8.84174 3.32187 8.56871 4.23266C8.3795 4.86177 7.93163 5.38524 7.33168 5.67749C6.46468 6.10003 5.94137 6.98969 6.00125 7.93687C6.04316 8.59179 5.80486 9.23381 5.34262 9.70916C4.6756 10.3969 4.49358 11.4099 4.88038 12.2784C5.14742 12.8793 5.14742 13.5636 4.88038 14.1645C4.49358 15.0342 4.6756 16.046 5.34262 16.7338C5.80366 17.2091 6.04196 17.8511 6.00125 18.5061C5.94137 19.4532 6.46588 20.3429 7.33168 20.7654C7.93163 21.0577 8.3795 21.5812 8.56871 22.2103C8.84294 23.1199 9.64527 23.7807 10.6069 23.8875C11.2715 23.9614 11.8762 24.303 12.2738 24.8311C12.8486 25.594 13.8342 25.9462 14.7742 25.7232C15.4244 25.5694 16.1118 25.6879 16.6687 26.0506C17.4746 26.5753 18.5236 26.5753 19.3295 26.0506C19.8876 25.6879 20.5737 25.5694 21.2252 25.7232C22.1664 25.945 23.1508 25.594 23.7256 24.8311C24.1231 24.303 24.7267 23.9614 25.3925 23.8875C26.3541 23.7807 27.1576 23.1211 27.4307 22.2103C27.6199 21.5812 28.0677 21.0577 28.6677 20.7654C29.5347 20.3429 30.058 19.4532 29.9981 18.5061C29.9562 17.8511 30.1945 17.2091 30.6568 16.7338C31.3238 16.046 31.5058 15.0342 31.119 14.1645ZM25.5326 11.7432L22.4131 14.7232L23.1496 18.9321C23.2741 19.6457 22.5101 20.1903 21.8563 19.8535L18.0003 17.8664L14.1443 19.8535C13.4905 20.1903 12.7265 19.6457 12.851 18.9321L13.5875 14.7232L10.468 11.7432C9.93866 11.2373 10.2308 10.357 10.9625 10.2526L15.2736 9.63874L17.2015 5.81012C17.5285 5.16106 18.4733 5.16106 18.8002 5.81012L20.7282 9.63874L25.0392 10.2526C25.7709 10.357 26.0619 11.2373 25.5338 11.7432H25.5326Z"
        fill="#002652"
      />
      <path
        d="M15.0546 26.8636C13.6428 27.197 12.1734 26.6735 11.31 25.528C11.1112 25.2639 10.8059 25.0913 10.473 25.0538C9.83111 24.9834 9.23954 24.7486 8.74257 24.3906L4.90815 32.3577C4.70817 32.7732 5.04826 33.2439 5.51529 33.1958L8.68509 32.8706C8.90783 32.8483 9.12578 32.9481 9.24912 33.1312L11.0095 35.7357C11.2681 36.1195 11.8561 36.0772 12.0561 35.6617L16.1647 27.123L16.1451 27.1117C16.0987 27.0849 16.0518 27.0578 16.0067 27.028C15.7276 26.846 15.3804 26.7862 15.0546 26.8636Z"
        fill="#002652"
      />
      <path
        d="M20.9453 26.8631C22.3572 27.1964 23.8265 26.673 24.6899 25.5274L24.6911 25.5263C24.8899 25.2622 25.1953 25.0885 25.5282 25.0521C26.1688 24.9817 26.7616 24.7469 27.2586 24.3889L31.0918 32.3572C31.2918 32.7727 30.9517 33.2433 30.4847 33.1952L27.3149 32.8701C27.0921 32.8466 26.8742 32.9476 26.7508 33.1307L24.9905 35.7351C24.7306 36.1189 24.1439 36.0767 23.9439 35.6612L19.8352 27.1225C19.8528 27.1119 19.8705 27.1016 19.8882 27.0913C19.9236 27.0707 19.959 27.0501 19.9933 27.0274C20.2723 26.8455 20.6196 26.7856 20.9453 26.8631Z"
        fill="#002652"
      />
    </svg>
  ),
  sliderControlIcon: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5", props.className)}
      xmlns="http://www.w3.org/2000/svg"
      width="36"
      height="36"
      viewBox="0 0 36 36"
      fill="none"
    >
      <g clipPath="url(#clip0_15616_6496)">
        <path
          d="M19.4211 27.7105V34.5789C19.4211 35.3842 18.8053 36 18 36C17.1947 36 16.5789 35.3842 16.5789 34.5789V27.7105C14.6368 27.0947 13.2632 25.2947 13.2632 23.2105C13.2632 21.1263 14.6368 19.2789 16.5789 18.7105V1.42105C16.5789 0.615791 17.1947 0 18 0C18.8053 0 19.4211 0.615791 19.4211 1.42105V18.7105C21.3632 19.3263 22.7368 21.1263 22.7368 23.2105C22.7368 25.2947 21.3632 27.1421 19.4211 27.7105ZM18 21.3149C16.9579 21.3149 16.1053 22.1676 16.1053 23.2097C16.1053 24.2518 16.9579 25.1044 18 25.1044C19.0421 25.1044 19.8947 24.2518 19.8947 23.2097C19.8947 22.1676 19.0421 21.3149 18 21.3149Z"
          fill="#002652"
        />
        <path
          d="M29.8421 9.71053V1.42105C29.8421 0.615791 30.4579 0 31.2632 0C32.0684 0 32.6842 0.615791 32.6842 1.42105V9.71053C34.6263 10.3263 36 12.1263 36 14.2105C36 16.2947 34.6263 18.1421 32.6842 18.7105V34.5789C32.6842 35.3842 32.0684 36 31.2632 36C30.4579 36 29.8421 35.3842 29.8421 34.5789V18.7105C27.9 18.0947 26.5263 16.2947 26.5263 14.2105C26.5263 12.1263 27.9 10.2789 29.8421 9.71053ZM31.2632 16.1053C32.3053 16.1053 33.1579 15.2526 33.1579 14.2105C33.1579 13.1684 32.3053 12.3158 31.2632 12.3158C30.2211 12.3158 29.3684 13.1684 29.3684 14.2105C29.3684 15.2526 30.2211 16.1053 31.2632 16.1053Z"
          fill="#002652"
        />
        <path
          d="M6.1579 1.42105V9.71053C8.1 10.3263 9.47368 12.1263 9.47368 14.2105C9.47368 16.2947 8.1 18.1421 6.1579 18.7105V34.5789C6.1579 35.3842 5.54211 36 4.73684 36C3.93158 36 3.31579 35.3842 3.31579 34.5789V18.7105C1.37368 18.0947 0 16.2947 0 14.2105C0 12.1263 1.37368 10.2789 3.31579 9.71053V1.42105C3.31579 0.615791 3.93158 0 4.73684 0C5.54211 0 6.1579 0.615791 6.1579 1.42105ZM4.73684 16.1053C5.77895 16.1053 6.63158 15.2526 6.63158 14.2105C6.63158 13.1684 5.77895 12.3158 4.73684 12.3158C3.69474 12.3158 2.84211 13.1684 2.84211 14.2105C2.84211 15.2526 3.69474 16.1053 4.73684 16.1053Z"
          fill="#002652"
        />
      </g>
      <defs>
        <clipPath id="clip0_15616_6496">
          <rect width="36" height="36" fill="white" />
        </clipPath>
      </defs>
    </svg>
  ),
  userProfileIcon: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5", props.className)}
      xmlns="http://www.w3.org/2000/svg"
      width="36"
      height="36"
      viewBox="0 0 36 36"
      fill="none"
    >
      <path
        d="M25.2942 17.7872C24.4335 16.8759 23.0735 16.5915 21.927 17.1054C20.6703 17.6673 19.2794 17.979 17.8161 17.979C16.3529 17.979 14.9585 17.6673 13.7052 17.1054C12.5587 16.5915 11.1988 16.8759 10.338 17.7872C8.61656 19.6132 7.55957 22.0695 7.55957 24.7691V32.5698C7.55957 33.9573 8.5718 35.1427 9.94898 35.3654C15.1616 36.2115 20.4775 36.2115 25.6902 35.3654C27.0673 35.1427 28.0796 33.9573 28.0796 32.5698V24.7691C28.0796 22.0661 27.0226 19.6132 25.3011 17.7872H25.2942Z"
        fill="#002652"
      />
      <path
        d="M17.8161 15.9235C22.2352 15.9235 25.8175 12.3589 25.8175 7.96175C25.8175 3.5646 22.2352 0 17.8161 0C13.3971 0 9.8147 3.5646 9.8147 7.96175C9.8147 12.3589 13.3971 15.9235 17.8161 15.9235Z"
        fill="#002652"
      />
    </svg>
  ),
  calculatorQuestionIcon: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5", props.className)}
      xmlns="http://www.w3.org/2000/svg"
      width="36"
      height="36"
      viewBox="0 0 36 36"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.32005 0H27.6804C30.4765 0 32.7602 2.27277 32.7602 5.05544V30.9446C32.7602 33.7272 30.4765 36 27.6804 36H8.32005C5.52395 36 3.24023 33.7272 3.24023 30.9446V5.05544C3.24023 2.27277 5.52395 0 8.32005 0ZM12.5727 28.8029C13.0558 28.8029 13.517 28.6135 13.8537 28.2784C14.1904 27.9361 14.3807 27.4844 14.3807 27.0036C14.3807 26.5229 14.1904 26.0639 13.8537 25.7289C13.5096 25.3938 13.0558 25.2044 12.5727 25.2044C12.0896 25.2044 11.6285 25.3938 11.2918 25.7289C10.9551 26.0712 10.7648 26.5229 10.7648 27.0036C10.7648 27.4844 10.9551 27.9433 11.2918 28.2784C11.6358 28.6135 12.0896 28.8029 12.5727 28.8029ZM12.5727 21.5986C13.0558 21.5986 13.517 21.4092 13.8537 21.0741C14.1904 20.7317 14.3807 20.2801 14.3807 19.7993C14.3807 19.3185 14.1904 18.8596 13.8537 18.5245C13.5096 18.1894 13.0558 18 12.5727 18C12.0896 18 11.6285 18.1894 11.2918 18.5245C10.9551 18.8669 10.7648 19.3185 10.7648 19.7993C10.7648 20.2801 10.9551 20.739 11.2918 21.0741C11.6358 21.4092 12.0896 21.5986 12.5727 21.5986ZM17.9966 28.8029C18.4797 28.8029 18.9408 28.6135 19.2775 28.2784C19.6142 27.9361 19.8045 27.4844 19.8045 27.0036C19.8045 26.5229 19.6142 26.0639 19.2775 25.7289C18.9335 25.3938 18.4797 25.2044 17.9966 25.2044C17.5135 25.2044 17.0523 25.3938 16.7156 25.7289C16.3789 26.0712 16.1886 26.5229 16.1886 27.0036C16.1886 27.4844 16.3789 27.9433 16.7156 28.2784C17.0597 28.6135 17.5135 28.8029 17.9966 28.8029ZM17.9966 21.5986C18.4797 21.5986 18.9408 21.4092 19.2775 21.0741C19.6142 20.7317 19.8045 20.2801 19.8045 19.7993C19.8045 19.3185 19.6142 18.8596 19.2775 18.5245C18.9335 18.1894 18.4797 18 17.9966 18C17.5135 18 17.0523 18.1894 16.7156 18.5245C16.3789 18.8669 16.1886 19.3185 16.1886 19.7993C16.1886 20.2801 16.3789 20.739 16.7156 21.0741C17.0597 21.4092 17.5135 21.5986 17.9966 21.5986ZM23.4204 28.8029C23.9035 28.8029 24.3646 28.6135 24.7013 28.2784C25.038 27.9361 25.2283 27.4844 25.2283 27.0036C25.2283 26.5229 25.038 26.0639 24.7013 25.7289C24.3573 25.3938 23.9035 25.2044 23.4204 25.2044C22.9373 25.2044 22.4762 25.3938 22.1395 25.7289C21.8028 26.0712 21.6125 26.5229 21.6125 27.0036C21.6125 27.4844 21.8028 27.9433 22.1395 28.2784C22.4835 28.6135 22.9373 28.8029 23.4204 28.8029ZM23.4204 21.5986C23.9035 21.5986 24.3646 21.4092 24.7013 21.0741C25.038 20.7317 25.2283 20.2801 25.2283 19.7993C25.2283 19.3185 25.038 18.8596 24.7013 18.5245C24.3573 18.1894 23.9035 18 23.4204 18C22.9373 18 22.4762 18.1894 22.1395 18.5245C21.8028 18.8669 21.6125 19.3185 21.6125 19.7993C21.6125 20.2801 21.8028 20.739 22.1395 21.0741C22.4835 21.4092 22.9373 21.5986 23.4204 21.5986ZM18.0216 5.76C20.926 5.76 23.0402 7.66588 23.0402 10.2947C23.0402 11.6967 22.485 12.792 21.3531 13.8217C21.1711 13.9844 20.9749 14.143 20.7793 14.301C20.0796 14.8662 19.3884 15.4247 19.3884 16.1438V16.56H16.2918V15.8152C16.2918 14.7856 16.9965 13.6902 18.3633 12.7263C19.367 12.0253 19.8582 11.3024 19.8582 10.5576C19.8582 9.61558 19.1107 8.82694 18.0002 8.82694C16.8897 8.82694 16.0782 9.70321 16.0568 10.9081H12.9602C12.9602 7.88495 15.0104 5.76 18.0216 5.76Z"
        fill="#002652"
      />
    </svg>
  ),
};
