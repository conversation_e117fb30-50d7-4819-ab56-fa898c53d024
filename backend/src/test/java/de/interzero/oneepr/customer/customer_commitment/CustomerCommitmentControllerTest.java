package de.interzero.oneepr.customer.customer_commitment;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.admin.service_setup.ServiceSetupService;
import de.interzero.oneepr.admin.service_setup.dto.CommitmentPackagingServiceDto;
import de.interzero.oneepr.admin.service_setup.dto.CommitmentSubmitResultDto;
import de.interzero.oneepr.admin.service_setup.dto.ServiceSetupCommitmentResponseDto;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.TestRole;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.customer_commitment.dto.CreateCustomerCommitmentDto;
import de.interzero.oneepr.customer.customer_commitment.dto.CustomerServiceSetup;
import de.interzero.oneepr.customer.customer_commitment.dto.FindCustomerCommitmentDto;
import de.interzero.oneepr.customer.customer_commitment.dto.UpdateCustomerCommitmentDto;
import de.interzero.oneepr.customer.shopping_cart.ShoppingCart;
import de.interzero.oneepr.customer.shopping_cart.ShoppingCartRepository;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.*;

import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Integration tests for the CustomerCommitmentController, focusing on happy path scenarios.
 * This class tests the successful request-response cycle for customer commitment endpoints.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class CustomerCommitmentControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private CustomerCommitmentRepository customerCommitmentRepository;

    @Autowired
    private ObjectMapper objectMapper;

    private Customer testCustomer;

    @Autowired
    private ShoppingCartRepository shoppingCartRepository;

    @MockBean
    private ServiceSetupService serviceSetupService;

    private CustomerCommitment testCommitment;

    @BeforeEach
    void setUp() {
        customerCommitmentRepository.deleteAll();
        customerRepository.deleteAll();

        testCustomer = new Customer();
        testCustomer.setUserId(101);
        testCustomer.setEmail("<EMAIL>");
        testCustomer.setFirstName("Test");
        testCustomer.setLastName("User");
        testCustomer.setCreatedAt(Instant.now());
        testCustomer.setUpdatedAt(Instant.now());
        testCustomer.setType(Customer.Type.REGULAR);
        testCustomer.setCustomerCommitment(new ArrayList<>());
        testCustomer = customerRepository.save(testCustomer);

        testCommitment = new CustomerCommitment();
        testCommitment.setCustomerEmail(testCustomer.getEmail());
        testCommitment.setCustomer(testCustomer);
        testCustomer.getCustomerCommitment().add(testCommitment);

        testCommitment.setCountryCode("DE");
        testCommitment.setYear(2025);
        testCommitment.setCommitment(new HashMap<>(Map.of("initial_key", "initial_value")));
        testCommitment.setServiceSetup(new CustomerServiceSetup());
        testCommitment.setIsLicenseRequired(true);
        testCommitment.setBlame(new HashMap<>(Map.of("user", "setup")));
        testCommitment.setCreatedAt(Instant.now());
        testCommitment.setUpdatedAt(Instant.now());

        testCommitment = customerCommitmentRepository.save(testCommitment);
    }

    /**
     * Test for {@link CustomerCommitmentController#create(CreateCustomerCommitmentDto)}.
     * This test mocks the direct call to the ServiceSetupService.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = "CUSTOMER"
    )
    void create_whenAuthenticated_shouldCreateCommitment() throws Exception {
        // 1. ARRANGE: Create the DTO for the request payload
        CreateCustomerCommitmentDto createDto = getCreateCustomerCommitmentDto();

        // 2. ARRANGE: Set up all database prerequisites
        // Create and save a Customer to satisfy the foreign key constraint
        Customer testCustomer1 = new Customer();
        testCustomer1.setEmail(createDto.getCustomerEmail());
        testCustomer1.setFirstName("Test");
        testCustomer1.setLastName("User");
        testCustomer1.setUserId(213);
        testCustomer1.setCreatedAt(Instant.now());
        testCustomer1.setUpdatedAt(Instant.now());
        customerRepository.saveAndFlush(testCustomer1);

        // Create and save a ShoppingCart to satisfy the foreign key constraint
        ShoppingCart testCart = new ShoppingCart();
        testCart.setId("test-cart-123");
        testCart.setCreatedAt(Instant.now());
        testCart.setUpdatedAt(Instant.now());
        testCart.setTotal(1000);
        testCart.setVatPercentage(20);
        testCart.setVatValue(200);
        testCart.setIsChurned(false);
        testCart.setJourney(ShoppingCart.Journey.LONG);
        testCart.setStatus(ShoppingCart.Status.OPEN);
        testCart.setSubtotal(100);
        testCart.setCouponValue(10);
        shoppingCartRepository.saveAndFlush(testCart);

        // Update the DTO with the real ID from the saved cart
        createDto.setShoppingCartId(testCart.getId());

        // 3. ARRANGE: Mock the response from the ServiceSetupService
        CommitmentPackagingServiceDto obligedService = new CommitmentPackagingServiceDto();
        obligedService.setObliged(true);

        CommitmentSubmitResultDto resultDto = new CommitmentSubmitResultDto();
        resultDto.setPackagingServices(List.of(obligedService));

        ServiceSetupCommitmentResponseDto mockResponse = new ServiceSetupCommitmentResponseDto();
        mockResponse.setYear(String.valueOf(createDto.getYear()));
        mockResponse.setSetup(resultDto);
        mockResponse.setCommitment(new ArrayList<>()); // Add empty list or mocked data as needed

        // Define the behavior of the mocked service
        when(serviceSetupService.submitServiceSetupCommitment(any(), any())).thenReturn(mockResponse);

        // 4. ACT & ASSERT
        mockMvc.perform(post(Api.CUSTOMER_COMMITMENTS).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id", is(notNullValue())))
                .andExpect(jsonPath("$.customer_email", is("<EMAIL>")))
                .andExpect(jsonPath("$.country_code", is("FR")));
    }

    /**
     * Helper method to generate a pre-populated and valid {@link CreateCustomerCommitmentDto}.
     * <p>
     * This factory method centralizes the creation of a standard DTO used across multiple tests,
     * ensuring consistency and reducing code duplication.
     *
     * @return A non-null instance of {@code CreateCustomerCommitmentDto} with standard test values.
     */
    private static @NotNull CreateCustomerCommitmentDto getCreateCustomerCommitmentDto() {
        CreateCustomerCommitmentDto.CommitmentAnswerDto commitmentAnswer = new CreateCustomerCommitmentDto.CommitmentAnswerDto();
        commitmentAnswer.setCriteriaId(1);
        commitmentAnswer.setAnswer("OBLIGED");
        commitmentAnswer.setToAnswer("ADDITIONAL_INFO");

        CreateCustomerCommitmentDto createDto = new CreateCustomerCommitmentDto();
        createDto.setCustomerEmail("<EMAIL>");
        createDto.setCountryCode("FR");
        createDto.setYear(2025);
        createDto.setCommitmentAnswers(List.of(commitmentAnswer));
        return createDto;
    }

    /**
     * Test for {@link CustomerCommitmentController#findAll(FindCustomerCommitmentDto)}
     * when an optional countryCode is also provided for filtering.
     */
    @Test
    @WithMockUser
    void findAll_withCountryCode_shouldReturnFilteredCommitments() throws Exception {
        mockMvc.perform(get(Api.CUSTOMER_COMMITMENTS).param("customerEmail", "<EMAIL>")
                                .param("year", "2025")
                                .param("countryCode", "DE"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].id", is(testCommitment.getId())))
                .andExpect(jsonPath("$[0].country_code", is("DE")));
    }

    /**
     * Test for {@link CustomerCommitmentController#findOne(Integer)}
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = "CUSTOMER"
    )
    void findOne_whenAuthenticatedAndAuthorized_shouldReturnCommitment() throws Exception {
        mockMvc.perform(get(Api.CUSTOMER_COMMITMENTS + "/" + testCommitment.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testCommitment.getId())))
                .andExpect(jsonPath("$.customer_email", is(testCustomer.getEmail())));
    }

    /**
     * Test for {@link CustomerCommitmentController#update(Integer, UpdateCustomerCommitmentDto)}
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = "CUSTOMER"
    )
    void update_whenAuthenticatedAndAuthorized_shouldUpdateCommitment() throws Exception {
        UpdateCustomerCommitmentDto updateDto = new UpdateCustomerCommitmentDto();
        updateDto.setCommitment(Map.of("updated_key", "updated_value"));
        updateDto.setServiceSetup(new CustomerServiceSetup());

        mockMvc.perform(put(Api.CUSTOMER_COMMITMENTS + "/" + testCommitment.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.commitment.updated_key", is("updated_value")))
                .andExpect(jsonPath("$.service_setup", notNullValue()));
    }

    /**
     * Test for {@link CustomerCommitmentController#remove(Integer)}
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = TestRole.ADMIN
    )
    void remove_whenUserIsAdmin_shouldSoftDeleteCommitment() throws Exception {
        mockMvc.perform(delete(Api.CUSTOMER_COMMITMENTS + "/" + testCommitment.getId()))
                .andExpect(status().isNoContent());

        CustomerCommitment deletedCommitment = customerCommitmentRepository.findById(testCommitment.getId())
                .orElseThrow();
        assertNotNull(deletedCommitment.getDeletedAt());
    }

    /**
     * Test for {@link CustomerCommitmentController#getStatuses(String, int, List)}.
     * This test verifies that the endpoint correctly returns a map of statuses,
     * identifying existing commitments as "COMPLETED" and non-existent ones as "OPEN".
     */
    @Test
    @WithMockUser
    void getStatuses_shouldReturnCorrectStatusMap_forMixedCases() throws Exception {

        // ACT & ASSERT
        mockMvc.perform(get(Api.CUSTOMER_COMMITMENTS + "/status").param("customer_email", "<EMAIL>")
                                .param("year", "2025")
                                .param("country_codes", "DE,FR,ES")).andExpect(status().isOk())
                // Verify that the response is a JSON array with 3 elements
                .andExpect(jsonPath("$", hasSize(3)))
                // Find the object with countryCode 'DE' and assert its status is 'COMPLETED'
                .andExpect(jsonPath("$[?(@.countryCode == 'DE')].status", contains("COMPLETED")))
                // Find the object with countryCode 'FR' and assert its status is 'OPEN'
                .andExpect(jsonPath("$[?(@.countryCode == 'FR')].status", contains("OPEN")))
                // Find the object with countryCode 'ES' and assert its status is 'OPEN'
                .andExpect(jsonPath("$[?(@.countryCode == 'ES')].status", contains("OPEN")));

    }
}
