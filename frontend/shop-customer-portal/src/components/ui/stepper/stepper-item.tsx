interface StepperItemProps {
  step: number;
  title: string;
  checked?: boolean;
  isCurrent?: boolean;
}

export function StepperItem({ step, title, checked = false, isCurrent = false }: StepperItemProps) {
  const isFirstItem = step === 1;

  return (
    <>
      {!isFirstItem && (
        <div className="divider flex h-[1px] min-w-2 w-9 rounded-sm bg-tonal-dark-cream-80 self-center" />
      )}
      <div className="group flex items-start" data-first={isFirstItem} data-checked={checked} data-current={isCurrent}>
        <div className="flex flex-col items-center">
          <div
            className={`flex justify-center items-center h-7 w-7 rounded-full ${
              isCurrent ? "bg-light-green" : checked ? "bg-tonal-dark-green-30" : "bg-tonal-dark-cream-80"
            }`}
          >
            <p
              className={`text-base font-centra ${isCurrent || checked || isFirstItem ? "font-bold" : "font-normal"} ${
                isCurrent
                  ? "text-tonal-dark-green-30"
                  : checked || isFirstItem
                    ? "text-white"
                    : "text-tonal-dark-cream-20"
              }`}
            >
              {step}
            </p>
          </div>
          <p
            className={`hidden lg:block mt-2 text-center text-base font-centra leading-tight ${
              isCurrent
                ? "text-primary font-bold"
                : checked
                  ? "text-tonal-dark-green-30 font-bold"
                  : "text-primary font-normal"
            }`}
          >
            {title}
          </p>
        </div>
      </div>
    </>
  );
}
