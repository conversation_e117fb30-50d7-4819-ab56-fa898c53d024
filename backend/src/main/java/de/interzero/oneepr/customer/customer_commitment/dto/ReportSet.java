package de.interzero.oneepr.customer.customer_commitment.dto;

import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.Instant;
import java.util.List;

/**
 * Originally found in src/customer-commitment/dto/customer-commitment.dto.ts
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonFilter("fractionIconFilter")
public class ReportSet {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("name")
    private String name;

    @JsonProperty("mode")
    private String mode;

    @JsonProperty("type")
    private String type;

    @JsonProperty("fractions")
    private List<Fraction> fractions;

    @JsonProperty("columns")
    private List<Column> columns;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonFilter("fractionIconFilter")
    public static class Fraction {

        @JsonProperty("id")
        private Integer id;

        @JsonProperty("name")
        private String name;

        @JsonProperty("description")
        private String description;

        @JsonProperty("icon")
        private String icon;

        @JsonProperty("is_active")
        private Boolean isActive;

        @JsonProperty("report_set_id")
        private Integer reportSetId;

        @JsonProperty("parent_id")
        private Integer parentId;

        @JsonProperty("children")
        private List<Fraction> children;

        @JsonProperty("fraction_icon_id")
        private String fractionIconId;

        @JsonProperty("fraction_icon_url")
        private String fractionIconUrl;

        @JsonProperty("level")
        private Integer level;

        @JsonProperty("order")
        private Integer order;

        @JsonProperty("code")
        private String code;

        @JsonProperty("created_at")
        private Instant createdAt;

        @JsonProperty("updated_at")
        private Instant updatedAt;

        @JsonProperty("deleted_at")
        private Instant deletedAt;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Column {

        @JsonProperty("id")
        private Integer id;

        @JsonProperty("name")
        private String name;

        @JsonProperty("description")
        private String description;

        @JsonProperty("unit_type")
        private String unitType;

        @JsonProperty("report_set_id")
        private Integer reportSetId;

        @JsonProperty("level")
        private Integer level;

        @JsonProperty("order")
        private Integer order;

        @JsonProperty("code")
        private String code;

        @JsonProperty("parent_id")
        private Integer parentId;

        @JsonProperty("created_at")
        private Instant createdAt;

        @JsonProperty("updated_at")
        private Instant updatedAt;

        @JsonProperty("deleted_at")
        private Instant deletedAt;

        @JsonProperty("children")
        private List<Column> children;
    }
}
