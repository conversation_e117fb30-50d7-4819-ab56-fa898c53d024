import { cn } from "@/lib/utils";
import { StepperItem } from "./stepper-item";

interface StepperProps {
  steps: Array<{ index: number; title: string }>;
  currentStep: number;
  className?: string;
}

export function Stepper({ steps, currentStep, className }: StepperProps) {
  return (
    <div className={cn("w-full overflow-x-auto", className)}>
      <div className="flex items-start justify-center gap-4 lg:gap-10 pb-6 min-w-full">
        {steps.map((step) => (
          <StepperItem
            key={`stepper_item_${step.index}`}
            step={step.index}
            title={step.title}
            checked={currentStep > step.index}
            isCurrent={currentStep === step.index}
          />
        ))}
      </div>
    </div>
  );
}
