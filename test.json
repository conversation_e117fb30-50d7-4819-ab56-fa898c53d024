{"year": "2025", "country": {"id": 1, "code": "AT", "name": "Austria", "flag_url": "https://cdn.kcak11.com/CountryFlags/countries/at.svg", "other_costs_obligated": false, "authorize_representative_obligated": false}, "price_list": null, "other_costs": [{"id": 1, "name": "cost 2", "price": 20000, "country_id": 1, "created_at": "2025-09-02T08:58:46.606Z", "deleted_at": null, "updated_at": "2025-09-02T08:58:46.606Z"}, {"id": 2, "name": "cost 1", "price": 10000, "country_id": 1, "created_at": "2025-09-02T08:58:46.606Z", "deleted_at": null, "updated_at": "2025-09-02T08:58:46.606Z"}], "packaging_services": [{"id": 1, "name": "Sales packaging", "obliged": false, "country_id": 1, "created_at": "2025-09-02T08:48:42.781Z", "deleted_at": null, "report_set": {"id": 1, "mode": "ON_PLATAFORM", "name": "Sales packaging A", "type": "FRACTIONS", "columns": [{"id": 1, "code": "9LURY1", "name": "col name", "level": 1, "order": 1, "parent_id": null, "unit_type": "KG", "created_at": "2025-09-02T08:52:47.054Z", "deleted_at": null, "updated_at": "2025-09-02T08:52:47.054Z", "description": "", "parent_code": null, "report_set_id": 1}, {"id": 2, "code": "ZL8BME", "name": "sub col 1", "level": 2, "order": 1, "parent_id": null, "unit_type": "KG", "created_at": "2025-09-02T08:52:47.056Z", "deleted_at": null, "updated_at": "2025-09-02T08:52:47.056Z", "description": "", "parent_code": "9LURY1", "report_set_id": 1}], "fractions": [{"id": 1, "code": "603T6C", "icon": "aluminium", "name": "Plastic", "level": 1, "order": 1, "children": [{"id": 2, "code": "PTLJGU", "icon": "aluminium", "name": "PE", "level": 2, "order": 1, "children": [{"id": 3, "code": "47EP6R", "icon": "aluminium", "name": "PE-A", "level": 3, "order": 1, "children": [], "is_active": true, "parent_id": null, "created_at": "2025-09-02T08:52:47.038Z", "deleted_at": null, "updated_at": "2025-09-02T08:52:47.038Z", "description": "PE-A", "parent_code": "PTLJGU", "report_set_id": 1, "has_third_level": false, "fraction_icon_id": 1, "has_second_level": false}], "is_active": true, "parent_id": null, "created_at": "2025-09-02T08:52:47.036Z", "deleted_at": null, "updated_at": "2025-09-02T08:52:47.036Z", "description": "PE", "parent_code": "603T6C", "report_set_id": 1, "has_third_level": false, "fraction_icon_id": 1, "has_second_level": false}], "is_active": true, "parent_id": null, "created_at": "2025-09-02T08:52:47.029Z", "deleted_at": null, "updated_at": "2025-09-02T08:52:47.029Z", "description": "Plastic", "parent_code": null, "report_set_id": 1, "has_third_level": true, "fraction_icon_id": 1, "has_second_level": true}, {"id": 2, "code": "PTLJGU", "icon": "aluminium", "name": "PE", "level": 2, "order": 1, "children": [{"id": 3, "code": "47EP6R", "icon": "aluminium", "name": "PE-A", "level": 3, "order": 1, "children": [], "is_active": true, "parent_id": null, "created_at": "2025-09-02T08:52:47.038Z", "deleted_at": null, "updated_at": "2025-09-02T08:52:47.038Z", "description": "PE-A", "parent_code": "PTLJGU", "report_set_id": 1, "has_third_level": false, "fraction_icon_id": 1, "has_second_level": false}], "is_active": true, "parent_id": null, "created_at": "2025-09-02T08:52:47.036Z", "deleted_at": null, "updated_at": "2025-09-02T08:52:47.036Z", "description": "PE", "parent_code": "603T6C", "report_set_id": 1, "has_third_level": false, "fraction_icon_id": 1, "has_second_level": false}, {"id": 3, "code": "47EP6R", "icon": "aluminium", "name": "PE-A", "level": 3, "order": 1, "children": [], "is_active": true, "parent_id": null, "created_at": "2025-09-02T08:52:47.038Z", "deleted_at": null, "updated_at": "2025-09-02T08:52:47.038Z", "description": "PE-A", "parent_code": "PTLJGU", "report_set_id": 1, "has_third_level": false, "fraction_icon_id": 1, "has_second_level": false}, {"id": 4, "code": "MR822Y", "icon": "aluminium", "name": "Metals", "level": 1, "order": 2, "children": [{"id": 5, "code": "TQQNIP", "icon": "aluminium", "name": "Iron", "level": 2, "order": 1, "children": [{"id": 6, "code": "8IIO4O", "icon": "aluminium", "name": "Iron-A", "level": 3, "order": 1, "children": [], "is_active": true, "parent_id": null, "created_at": "2025-09-02T08:52:47.046Z", "deleted_at": null, "updated_at": "2025-09-02T08:52:47.046Z", "description": "", "parent_code": "TQQNIP", "report_set_id": 1, "has_third_level": false, "fraction_icon_id": 1, "has_second_level": false}], "is_active": true, "parent_id": null, "created_at": "2025-09-02T08:52:47.040Z", "deleted_at": null, "updated_at": "2025-09-02T08:52:47.040Z", "description": "", "parent_code": "MR822Y", "report_set_id": 1, "has_third_level": false, "fraction_icon_id": 1, "has_second_level": false}], "is_active": true, "parent_id": null, "created_at": "2025-09-02T08:52:47.039Z", "deleted_at": null, "updated_at": "2025-09-02T08:52:47.039Z", "description": "Metals", "parent_code": null, "report_set_id": 1, "has_third_level": true, "fraction_icon_id": 1, "has_second_level": true}, {"id": 5, "code": "TQQNIP", "icon": "aluminium", "name": "Iron", "level": 2, "order": 1, "children": [{"id": 6, "code": "8IIO4O", "icon": "aluminium", "name": "Iron-A", "level": 3, "order": 1, "children": [], "is_active": true, "parent_id": null, "created_at": "2025-09-02T08:52:47.046Z", "deleted_at": null, "updated_at": "2025-09-02T08:52:47.046Z", "description": "", "parent_code": "TQQNIP", "report_set_id": 1, "has_third_level": false, "fraction_icon_id": 1, "has_second_level": false}], "is_active": true, "parent_id": null, "created_at": "2025-09-02T08:52:47.040Z", "deleted_at": null, "updated_at": "2025-09-02T08:52:47.040Z", "description": "", "parent_code": "MR822Y", "report_set_id": 1, "has_third_level": false, "fraction_icon_id": 1, "has_second_level": false}, {"id": 6, "code": "8IIO4O", "icon": "aluminium", "name": "Iron-A", "level": 3, "order": 1, "children": [], "is_active": true, "parent_id": null, "created_at": "2025-09-02T08:52:47.046Z", "deleted_at": null, "updated_at": "2025-09-02T08:52:47.046Z", "description": "", "parent_code": "TQQNIP", "report_set_id": 1, "has_third_level": false, "fraction_icon_id": 1, "has_second_level": false}], "created_at": "2025-09-02T08:50:17.306Z", "deleted_at": null, "updated_at": "2025-09-02T08:52:47.062Z", "price_lists": [{"id": 1, "type": "PRICE_PER_VOLUME_BASE_PRICE", "items": [{"id": 1, "price": 30000, "created_at": "2025-09-02T08:52:47.110Z", "deleted_at": null, "updated_at": "2025-09-02T08:52:47.110Z", "fraction_code": "603T6C", "price_list_id": 1}, {"id": 2, "price": 40000, "created_at": "2025-09-02T08:52:47.112Z", "deleted_at": null, "updated_at": "2025-09-02T08:52:47.112Z", "fraction_code": "47EP6R", "price_list_id": 1}, {"id": 3, "price": 50000, "created_at": "2025-09-02T08:52:47.113Z", "deleted_at": null, "updated_at": "2025-09-02T08:52:47.113Z", "fraction_code": "MR822Y", "price_list_id": 1}, {"id": 4, "price": 60000, "created_at": "2025-09-02T08:52:47.113Z", "deleted_at": null, "updated_at": "2025-09-02T08:52:47.113Z", "fraction_code": "8IIO4O", "price_list_id": 1}], "title": "priceA", "end_date": "3001-01-01T00:00:00Z", "base_price": 1000, "created_at": "2025-09-02T08:52:47.077Z", "deleted_at": null, "start_date": "2025-01-01T00:00:00Z", "updated_at": "2025-09-02T08:52:47.077Z", "fixed_price": null, "minimum_fee": 2000, "license_year": 2025, "report_set_id": 1}], "sheet_file_id": null, "packaging_service_id": 1, "sheet_file_description": null}, "updated_at": "2025-09-02T08:48:42.781Z", "description": "Sales packaging", "report_set_frequency": {"id": 1, "rhythm": "ANNUALLY", "frequency": {"open": {"day": 1, "month": "JANUARY"}, "deadline": {"day": 1, "month": "JANUARY"}}, "created_at": "2025-09-02T08:57:59.048Z", "deleted_at": null, "updated_at": "2025-09-02T08:57:59.048Z", "has_criteria": false, "packaging_service": null, "packaging_service_id": 1}}, {"id": 2, "name": "Transport packaging", "obliged": true, "country_id": 1, "created_at": "2025-09-02T08:48:49.102Z", "deleted_at": null, "report_set": {"id": 3, "mode": "ON_PLATAFORM", "name": "Transport packaging A", "type": "FRACTIONS", "columns": [{"id": 7, "code": "6KGEIW", "name": "col name", "level": 1, "order": 1, "parent_id": null, "unit_type": "KG", "created_at": "2025-09-02T08:56:39.054Z", "deleted_at": null, "updated_at": "2025-09-02T08:56:39.054Z", "description": "", "parent_code": null, "report_set_id": 3}, {"id": 8, "code": "FQSNWF", "name": "sub col 1", "level": 2, "order": 1, "parent_id": null, "unit_type": "KG", "created_at": "2025-09-02T08:56:39.058Z", "deleted_at": null, "updated_at": "2025-09-02T08:56:39.058Z", "description": "", "parent_code": "6KGEIW", "report_set_id": 3}], "fractions": [{"id": 19, "code": "6UA7BE", "icon": "aluminium", "name": "Plastic", "level": 1, "order": 1, "children": [{"id": 20, "code": "UA5IC2", "icon": "aluminium", "name": "PE", "level": 2, "order": 1, "children": [{"id": 21, "code": "BGGVZT", "icon": "aluminium", "name": "PE-A", "level": 3, "order": 1, "children": [], "is_active": true, "parent_id": null, "created_at": "2025-09-02T08:56:39.050Z", "deleted_at": null, "updated_at": "2025-09-02T08:56:39.050Z", "description": "", "parent_code": "UA5IC2", "report_set_id": 3, "has_third_level": false, "fraction_icon_id": 1, "has_second_level": false}], "is_active": true, "parent_id": null, "created_at": "2025-09-02T08:56:39.047Z", "deleted_at": null, "updated_at": "2025-09-02T08:56:39.047Z", "description": "", "parent_code": "6UA7BE", "report_set_id": 3, "has_third_level": false, "fraction_icon_id": 1, "has_second_level": false}], "is_active": true, "parent_id": null, "created_at": "2025-09-02T08:56:39.038Z", "deleted_at": null, "updated_at": "2025-09-02T08:56:39.038Z", "description": "Plastic", "parent_code": null, "report_set_id": 3, "has_third_level": true, "fraction_icon_id": 1, "has_second_level": true}, {"id": 20, "code": "UA5IC2", "icon": "aluminium", "name": "PE", "level": 2, "order": 1, "children": [{"id": 21, "code": "BGGVZT", "icon": "aluminium", "name": "PE-A", "level": 3, "order": 1, "children": [], "is_active": true, "parent_id": null, "created_at": "2025-09-02T08:56:39.050Z", "deleted_at": null, "updated_at": "2025-09-02T08:56:39.050Z", "description": "", "parent_code": "UA5IC2", "report_set_id": 3, "has_third_level": false, "fraction_icon_id": 1, "has_second_level": false}], "is_active": true, "parent_id": null, "created_at": "2025-09-02T08:56:39.047Z", "deleted_at": null, "updated_at": "2025-09-02T08:56:39.047Z", "description": "", "parent_code": "6UA7BE", "report_set_id": 3, "has_third_level": false, "fraction_icon_id": 1, "has_second_level": false}, {"id": 21, "code": "BGGVZT", "icon": "aluminium", "name": "PE-A", "level": 3, "order": 1, "children": [], "is_active": true, "parent_id": null, "created_at": "2025-09-02T08:56:39.050Z", "deleted_at": null, "updated_at": "2025-09-02T08:56:39.050Z", "description": "", "parent_code": "UA5IC2", "report_set_id": 3, "has_third_level": false, "fraction_icon_id": 1, "has_second_level": false}], "created_at": "2025-09-02T08:55:41.699Z", "deleted_at": null, "updated_at": "2025-09-02T08:56:39.065Z", "price_lists": [{"id": 3, "type": "PRICE_PER_VOLUME_BASE_PRICE", "items": [{"id": 13, "price": 9000, "created_at": "2025-09-02T08:56:39.082Z", "deleted_at": null, "updated_at": "2025-09-02T08:56:39.082Z", "fraction_code": "6UA7BE", "price_list_id": 3}, {"id": 14, "price": 10000, "created_at": "2025-09-02T08:56:39.083Z", "deleted_at": null, "updated_at": "2025-09-02T08:56:39.083Z", "fraction_code": "BGGVZT", "price_list_id": 3}], "title": "PriceB", "end_date": "3001-01-01T00:00:00Z", "base_price": 400, "created_at": "2025-09-02T08:56:39.073Z", "deleted_at": null, "start_date": "2025-01-01T00:00:00Z", "updated_at": "2025-09-02T08:56:39.073Z", "fixed_price": null, "minimum_fee": 800, "license_year": 2025, "report_set_id": 3}], "sheet_file_id": null, "packaging_service_id": 2, "sheet_file_description": null}, "updated_at": "2025-09-02T08:48:49.102Z", "description": "Transport packaging", "report_set_frequency": {"id": 3, "rhythm": "QUARTERLY", "frequency": "{\"open\": {\"option\": \"FIRST\", \"weekDay\": \"MONDAY\"}, \"deadline\": {\"option\": \"LAST\", \"weekDay\": \"MONDAY\"}}", "created_at": "2025-09-02T08:57:59.048Z", "deleted_at": null, "updated_at": "2025-09-02T08:57:59.048Z", "has_criteria": false, "packaging_service": null, "packaging_service_id": 2}}], "representative_tier": null, "other_costs_obligated": false, "required_informations": [{"id": 1, "kind": "COUNTRY_INFORMATION", "name": "Require text for sales packaging", "type": "TEXT", "file_id": null, "question": null, "country_id": 1, "created_at": "2025-09-02T09:01:06.954Z", "deleted_at": null, "updated_at": "2025-09-02T09:01:06.954Z", "description": "Require text for sales packaging help", "packaging_service_ids": [1], "hibernateLazyInitializer": {}}], "authorize_representative_obligated": false}