"use client";

import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";
import { ShopBreadcrumb } from "@/components/modules/shop/components/shop-breadcrumb";
import { ShopContent } from "@/components/modules/shop/components/shop-content";
import { ShopLongJourneyStepper } from "@/components/modules/shop/components/shop-stepper";
import { JourneyPurchase } from "@/components/modules/shop/journeys/components/journey-purchase";
import { Plant } from "@arthursenno/lizenzero-ui-react/Icon";
import { useLongJourneyPath } from "@/hooks/path/use-long-journey-path";

import { useTranslations } from "next-intl";
export default function LicenseObligationsPurchase() {
  const t = useTranslations("shop.longJourney.purchase");

  return (
    <>
      <ShopBreadcrumb paths={useLongJourneyPath()} Icon={Plant} />
      <ShopLongJourneyStepper stepById={"review-order"} />
      <ShopContent>
        <TitleAndSubTitle title={t("title")} subTitle={t("subTitle")} />
        <JourneyPurchase journey="LONG" />
      </ShopContent>
    </>
  );
}
