{"shoppingCartId": "60bedfa0-e3da-4ea8-ac9d-fa6d341c6aea", "id": 3, "customer_email": "<EMAIL>", "country_code": "AT", "year": 2025, "commitment": {"1": "NOT_OBLIGED", "2": "1", "3": "4", "5": "2"}, "service_setup": {"year": "2025", "country": {"id": 1, "code": "AT", "name": "Austria", "flag_url": "https://cdn.kcak11.com/CountryFlags/countries/at.svg", "other_costs_obligated": false, "authorize_representative_obligated": false}, "price_list": null, "other_costs": [{"id": 2, "name": "cost 1", "price": 10000, "country_id": 1, "created_at": "2025-09-02T08:58:46.606Z", "deleted_at": null, "updated_at": "2025-09-02T08:58:46.606Z"}], "packaging_services": [{"id": 1, "name": "Sales packaging", "obliged": false, "country_id": 1, "created_at": "2025-09-02T08:48:42.781Z", "deleted_at": null, "report_set": null, "updated_at": "2025-09-02T08:48:42.781Z", "description": "Sales packaging", "report_set_frequency": null}, {"id": 2, "name": "Transport packaging", "obliged": false, "country_id": 1, "created_at": "2025-09-02T08:48:49.102Z", "deleted_at": null, "report_set": null, "updated_at": "2025-09-02T08:48:49.102Z", "description": "Transport packaging", "report_set_frequency": {"id": 3, "rhythm": "QUARTERLY", "frequency": "{\"open\": {\"option\": \"FIRST\", \"weekDay\": \"MONDAY\"}, \"deadline\": {\"option\": \"LAST\", \"weekDay\": \"MONDAY\"}}", "created_at": "2025-09-02T08:57:59.048Z", "deleted_at": null, "updated_at": "2025-09-02T08:57:59.048Z", "has_criteria": false, "packaging_service": null, "packaging_service_id": 2}}], "representative_tier": null, "other_costs_obligated": false, "required_informations": [{"id": 1, "kind": "COUNTRY_INFORMATION", "name": "Require text for sales packaging", "type": "TEXT", "file_id": null, "question": null, "country_id": 1, "created_at": "2025-09-02T09:01:06.954Z", "deleted_at": null, "updated_at": "2025-09-02T09:01:06.954Z", "description": "Require text for sales packaging help", "packaging_service_ids": [1], "hibernateLazyInitializer": {}}], "authorize_representative_obligated": false}, "is_license_required": false, "blame": {}, "created_at": "2025-09-08T11:20:28.461Z", "updated_at": "2025-09-08T14:36:06.158Z", "deleted_at": null, "shopping_cart": {"hibernateLazyInitializer": {}, "id": "60bedfa0-e3da-4ea8-ac9d-fa6d341c6aea", "email": "<EMAIL>", "created_at": "2025-09-08T10:15:31.258Z", "updated_at": "2025-09-08T10:16:13.954Z", "is_churned": false, "journey": "LONG", "journey_step": "SHOPPING_CART", "payment": null, "status": "OPEN", "subtotal": 200, "total": 200, "vat_percentage": 0, "vat_value": 0, "coupon": null, "coupon_type": null, "coupon_value": 0, "coupon_url_link": null, "customer": {"id": 2, "type": "REGULAR", "first_name": "test", "last_name": "test", "salutation": null, "email": "<EMAIL>", "user_id": 1, "is_active": null, "document_id": null, "id_stripe": null, "created_at": "2025-09-04T19:27:40Z", "updated_at": "2025-09-04T19:27:42Z", "deleted_at": null, "company_name": null, "language": null, "currency": null}, "items": [{"id": 16, "country_id": 1, "country_code": "AT", "country_name": "Austria", "country_flag": "https://cdn.kcak11.com/CountryFlags/countries/at.svg", "year": 2025, "price_list": {"id": 1, "name": "AT price list", "type": "EU_LICENSE", "price": null, "end_date": "2025-12-31T00:00:00Z", "created_at": "2025-09-04T11:04:32.128Z", "deleted_at": null, "start_date": "2025-01-01T00:00:00Z", "thresholds": null, "updated_at": "2025-09-04T11:04:32.128Z", "basic_price": null, "description": "at price list desc", "handling_fee": 100, "minimum_price": null, "condition_type": "LICENSE_YEAR", "registration_fee": 100, "condition_type_value": "2025", "variable_handling_fee": 1}, "packaging_services": null, "created_at": "2025-09-08T10:16:01.844Z", "updated_at": "2025-09-08T10:16:13.944Z", "deleted_at": null, "calculator": null, "price": 200, "service_type": "EU_LICENSE", "specification_type": "PURCHASE"}], "customer_commitments": [{"shoppingCartId": "60bedfa0-e3da-4ea8-ac9d-fa6d341c6aea", "id": 3, "customer_email": "<EMAIL>", "country_code": "AT", "year": 2025, "commitment": {"1": "NOT_OBLIGED", "2": "1", "3": "4", "5": "2"}, "service_setup": {"year": "2025", "country": {"id": 1, "code": "AT", "name": "Austria", "flag_url": "https://cdn.kcak11.com/CountryFlags/countries/at.svg", "other_costs_obligated": false, "authorize_representative_obligated": false}, "price_list": null, "other_costs": [{"id": 2, "name": "cost 1", "price": 10000, "country_id": 1, "created_at": "2025-09-02T08:58:46.606Z", "deleted_at": null, "updated_at": "2025-09-02T08:58:46.606Z"}], "packaging_services": [{"id": 1, "name": "Sales packaging", "obliged": false, "country_id": 1, "created_at": "2025-09-02T08:48:42.781Z", "deleted_at": null, "report_set": null, "updated_at": "2025-09-02T08:48:42.781Z", "description": "Sales packaging", "report_set_frequency": null}, {"id": 2, "name": "Transport packaging", "obliged": false, "country_id": 1, "created_at": "2025-09-02T08:48:49.102Z", "deleted_at": null, "report_set": null, "updated_at": "2025-09-02T08:48:49.102Z", "description": "Transport packaging", "report_set_frequency": {"id": 3, "rhythm": "QUARTERLY", "frequency": "{\"open\": {\"option\": \"FIRST\", \"weekDay\": \"MONDAY\"}, \"deadline\": {\"option\": \"LAST\", \"weekDay\": \"MONDAY\"}}", "created_at": "2025-09-02T08:57:59.048Z", "deleted_at": null, "updated_at": "2025-09-02T08:57:59.048Z", "has_criteria": false, "packaging_service": null, "packaging_service_id": 2}}], "representative_tier": null, "other_costs_obligated": false, "required_informations": [{"id": 1, "kind": "COUNTRY_INFORMATION", "name": "Require text for sales packaging", "type": "TEXT", "file_id": null, "question": null, "country_id": 1, "created_at": "2025-09-02T09:01:06.954Z", "deleted_at": null, "updated_at": "2025-09-02T09:01:06.954Z", "description": "Require text for sales packaging help", "packaging_service_ids": [1], "hibernateLazyInitializer": {}}], "authorize_representative_obligated": false}, "is_license_required": false, "blame": {}, "created_at": "2025-09-08T11:20:28.461Z", "updated_at": "2025-09-08T14:36:06.158Z", "deleted_at": null, "shopping_cart_id": "60bedfa0-e3da-4ea8-ac9d-fa6d341c6aea"}], "affiliate_link": null, "affiliate_type": null}, "shopping_cart_id": "60bedfa0-e3da-4ea8-ac9d-fa6d341c6aea"}