package de.interzero.oneepr.customer.customer_commitment.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.customer.entity.LicenseReportSetFrequency;
import lombok.Data;

/**
 * Originally found in src/customer-commitment/dto/customer-commitment.dto.ts
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReportSetFrequency {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("rhythm")
    private LicenseReportSetFrequency.Rhythm rhythm;

    @JsonProperty("frequency")
    private Frequency frequency;

    @JsonProperty("packaging_service_id")
    private Integer packagingServiceId;

    @JsonProperty("created_at")
    private String createdAt;

    @JsonProperty("updated_at")
    private String updatedAt;

    @JsonProperty("deleted_at")
    private String deletedAt;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Frequency {

        @JsonProperty("deadline")
        private DateConfig deadline;

        @JsonProperty("open")
        private DateConfig open;

        // Default constructor for Jackson
        public Frequency() {
        }

        // Constructor for string deserialization
        @JsonCreator
        public Frequency(String jsonString) {
            try {
                ObjectMapper mapper = new ObjectMapper();
                Frequency temp = mapper.readValue(jsonString, Frequency.class);
                this.deadline = temp.deadline;
                this.open = temp.open;
            } catch (Exception e) {
                throw new IllegalArgumentException("Failed to parse Frequency from JSON string: " + jsonString, e);
            }
        }
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DateConfig {

        @JsonProperty("day")
        private Integer day;

        @JsonProperty("month")
        private String month;

        @JsonProperty("option")
        private String option;

        @JsonProperty("weekDay")
        private String weekDay;
    }
}
